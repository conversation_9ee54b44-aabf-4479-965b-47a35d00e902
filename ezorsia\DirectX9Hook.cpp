#include "stdafx.h"
#include <vector>
#include <algorithm>
#include <Windows.h>
#include <ctime>
#include <d3dx9.h>
#include "BossHP.h"
#include "Bodyrelmove.h"
#include "GameObject.h"
#pragma comment(lib, "d3dx9.lib")
// 添加编码转换函数
std::string GBKToUTF8(const std::string& gbk_str) {
	// 先将 GBK 转换为 Unicode
	int len = MultiByteToWideChar(CP_ACP, 0, gbk_str.c_str(), -1, NULL, 0);
	if (len == 0) return "";

	std::wstring unicode_str(len, 0);
	MultiByteToWideChar(CP_ACP, 0, gbk_str.c_str(), -1, &unicode_str[0], len);

	// 再将 Unicode 转换为 UTF-8
	len = WideCharToMultiByte(CP_UTF8, 0, unicode_str.c_str(), -1, NULL, 0, NULL, NULL);
	if (len == 0) return "";

	std::string utf8_str(len, 0);
	WideCharToMultiByte(CP_UTF8, 0, unicode_str.c_str(), -1, &utf8_str[0], len, NULL, NULL);

	return utf8_str;
}


LPDIRECT3DTEXTURE9 DirectX9Hook::texture_cursor = nullptr;
LPDIRECT3DTEXTURE9 DirectX9Hook::texture_cursor_clicked = nullptr;
LPDIRECT3DTEXTURE9 DirectX9Hook::texture_bosshpbar = nullptr;
LPDIRECT3DTEXTURE9 DirectX9Hook::texture_background = nullptr;
LPDIRECT3DTEXTURE9 DirectX9Hook::there_background = nullptr;
// 排名数字背景纹理定义
LPDIRECT3DTEXTURE9 DirectX9Hook::texture_rank1 = nullptr;
LPDIRECT3DTEXTURE9 DirectX9Hook::texture_rank2 = nullptr;
LPDIRECT3DTEXTURE9 DirectX9Hook::texture_rank3 = nullptr;
LPDIRECT3DTEXTURE9 DirectX9Hook::texture_rank4 = nullptr;
LPDIRECT3DTEXTURE9 DirectX9Hook::texture_rank5 = nullptr;
LPDIRECT3DTEXTURE9 DirectX9Hook::texture_rank6 = nullptr;
LPDIRECT3DTEXTURE9 DirectX9Hook::texture_rank7 = nullptr;
LPDIRECT3DTEXTURE9 DirectX9Hook::texture_rank8 = nullptr;
LPDIRECT3DTEXTURE9 DirectX9Hook::texture_rank9 = nullptr;
LPDIRECT3DTEXTURE9 DirectX9Hook::texture_rank10 = nullptr;


LPDIRECT3DTEXTURE9 texture_cursor_anim1 = nullptr;
LPDIRECT3DTEXTURE9 texture_cursor_anim2 = nullptr;
LPDIRECT3DTEXTURE9 texture_cursor_anim3 = nullptr;
LPDIRECT3DTEXTURE9 texture_cursor_anim4 = nullptr;
LPDIRECT3DTEXTURE9 DirectX9Hook::there_background_anim0 = nullptr; // <--- 新增
LPDIRECT3DTEXTURE9 DirectX9Hook::there_background_anim1 = nullptr; // <--- 新增
LPDIRECT3DTEXTURE9 DirectX9Hook::there_background_anim2 = nullptr; // <--- 新增
LPDIRECT3DTEXTURE9 DirectX9Hook::there_background_anim3 = nullptr; // <--- 新增
LPDIRECT3DTEXTURE9 DirectX9Hook::there_background_anim4 = nullptr; // <--- 新增
LPDIRECT3DTEXTURE9 DirectX9Hook::there_background_anim5 = nullptr; // <--- 新增
LPDIRECT3DTEXTURE9 DirectX9Hook::there_background_anim6 = nullptr; // <--- 新增
LPDIRECT3DTEXTURE9 DirectX9Hook::there_background_anim7 = nullptr; // <--- 新增
LPDIRECT3DTEXTURE9 DirectX9Hook::button_background = nullptr;
LPDIRECT3DTEXTURE9 DirectX9Hook::button_background_hovered = nullptr;
LPDIRECT3DTEXTURE9 DirectX9Hook::button_background_pressed = nullptr;
static LPDIRECT3DTEXTURE9 bossDpsWindowBackground = nullptr;
static LPDIRECT3DTEXTURE9 bossDpsCloseBtnTextureNormal = nullptr;
static LPDIRECT3DTEXTURE9 bossDpsCloseBtnTextureHovered = nullptr;
static LPDIRECT3DTEXTURE9 bossDpsCloseBtnTexturePressed = nullptr;
static LPDIRECT3DTEXTURE9 bossDpsCloseBtnTextureDisabled = nullptr;
LPDIRECT3DTEXTURE9 CreateTextureFromWzCanvas(IWzCanvasPtr pCanvas, LPDIRECT3DDEVICE9 pDevice)
{
	if (!pCanvas) return nullptr;

	// 获取画布尺寸
	uint32_t width = 0, height = 0;
	pCanvas->get_width(&width);
	pCanvas->get_height(&height);

	// 获取像素格式
	int pixelFormatInt = 0;
	pCanvas->get_pixelFormat(&pixelFormatInt);
	CANVAS_PIXFORMAT pixelFormat = static_cast<CANVAS_PIXFORMAT>(pixelFormatInt);

	// 根据WZ格式选择DirectX格式和字节数
	D3DFORMAT d3dFormat;
	int destBytesPerPixel;

	switch (pixelFormat) {
	case CP_A4R4G4B4:
		d3dFormat = D3DFMT_A4R4G4B4;
		destBytesPerPixel = 2;
		break;
	case CP_A8R8G8B8:
		d3dFormat = D3DFMT_A8R8G8B8;
		destBytesPerPixel = 4;
		break;
	case CP_R5G6B5:
		d3dFormat = D3DFMT_R5G6B5;
		destBytesPerPixel = 2;
		break;
	default:
		// 默认使用32位格式
		d3dFormat = D3DFMT_A8R8G8B8;
		destBytesPerPixel = 4;
		break;
	}

	// 创建纹理
	LPDIRECT3DTEXTURE9 texture = nullptr;
	if (FAILED(pDevice->CreateTexture(width, height, 1, 0, d3dFormat, D3DPOOL_MANAGED, &texture, nullptr)))
		return nullptr;

	// 锁定纹理
	D3DLOCKED_RECT lockedRect;
	if (FAILED(texture->LockRect(0, &lockedRect, nullptr, 0)))
	{
		texture->Release();
		return nullptr;
	}

	// 获取tile尺寸
	uint32_t tileWidth = 0, tileHeight = 0;
	pCanvas->get_tileWidth(&tileWidth);
	pCanvas->get_tileHeight(&tileHeight);

	if (tileWidth == 0 || tileHeight == 0)
	{
		texture->UnlockRect(0);
		texture->Release();
		return nullptr;
	}

	// 计算需要的tile数量
	int tileCountX = (width + tileWidth - 1) / tileWidth;
	int tileCountY = (height + tileHeight - 1) / tileHeight;

	// 辅助函数：根据格式获取每像素字节数
	auto getBytesPerPixel = [](CANVAS_PIXFORMAT format) -> int {
		switch (format) {
		case CP_A4R4G4B4:
		case CP_R5G6B5:
			return 2;
		case CP_A8R8G8B8:
			return 4;
		default:
			return 2;
		}
		};

	// 逐个tile处理
	for (int tileY = 0; tileY < tileCountY; tileY++)
	{
		for (int tileX = 0; tileX < tileCountX; tileX++)
		{
			// 获取当前tile的RawCanvas
			IWzRawCanvas* rawCanvas = nullptr;
			pCanvas->get_rawCanvas(tileX * tileWidth, tileY * tileHeight, &rawCanvas);
			if (!rawCanvas)
				continue;

			CANVAS_PIXFORMAT rawPixelFormat;
			rawCanvas->get_pixelFormat(&rawPixelFormat);
			int bytesPerPixel = getBytesPerPixel(rawPixelFormat);

			// 获取RawCanvas尺寸
			uint32_t rawWidth = 0, rawHeight = 0;
			rawCanvas->get_width(&rawWidth);
			rawCanvas->get_height(&rawHeight);

			// 锁定RawCanvas
			int pitch;
			VARIANT var;
			VariantInit(&var);
			rawCanvas->raw__LockAddress(&pitch, &var);
			uint8_t* srcData = var.pbVal;

			if (srcData)
			{
				// 计算当前tile在纹理中的位置
				int destX = tileX * tileWidth;
				int destY = tileY * tileHeight;

				// 复制数据到纹理
				for (uint32_t y = 0; y < rawHeight; y++)
				{
					if (destY + y >= height)
						break;

					uint8_t* destRow = (uint8_t*)lockedRect.pBits + (destY + y) * lockedRect.Pitch + destX * destBytesPerPixel;
					uint8_t* srcRow = srcData + y * pitch;

					// 确保不超出纹理宽度
					uint32_t copyWidth = min(rawWidth, width - destX);

					// 如果源格式和目标格式字节数相同，直接复制
					if (bytesPerPixel == destBytesPerPixel) {
						memcpy(destRow, srcRow, copyWidth * bytesPerPixel);
					}
					else {
						// 需要格式转换
						// 这里可以添加格式转换代码，如A8R8G8B8到A4R4G4B4的转换
						// 简单起见，这里仅复制相同字节数
						memcpy(destRow, srcRow, copyWidth * min(bytesPerPixel, destBytesPerPixel));
					}
				}
			}

			// 清理
			rawCanvas->raw__UnlockAddress(nullptr);
			if (var.vt == 8 && var.decVal.Lo32)
				CoTaskMemFree((void*)(var.decVal.Lo32 - 4));
			else
				VariantClear(&var);

			rawCanvas->Release();
		}
	}

	// 解锁纹理
	texture->UnlockRect(0);

	return texture;
}

LPDIRECT3DTEXTURE9 LoadTextureFromPath(LPDIRECT3DDEVICE9 pDevice, wchar_t* path)
{
	if (!pDevice || !path) {
		return nullptr;
	}

	Ztl_variant_t vEmpty;
	Ztl_variant_t vTexture;
	CHECK_HRESULT(get_rm()->raw_GetObject(path, vEmpty, vEmpty, &vTexture));
	IWzCanvasPtr pCanvas = IWzCanvasPtr(vTexture.GetUnknown(false, false));
	return CreateTextureFromWzCanvas(pCanvas, pDevice);
}



// 在CURSOR_CLICKED数组后添加两个新的图片数组
const unsigned char START_BUTTON[] = {
	// 这里需要添加开始按钮的图片数据
 0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52,
0x00, 0x00, 0x00, 0xBF, 0x00, 0x00, 0x00, 0x24, 0x08, 0x06, 0x00, 0x00, 0x00, 0xD8, 0x4B, 0xEB,
0x86, 0x00, 0x00, 0x00, 0x01, 0x73, 0x52, 0x47, 0x42, 0x00, 0xAE, 0xCE, 0x1C, 0xE9, 0x00, 0x00,
0x00, 0x04, 0x67, 0x41, 0x4D, 0x41, 0x00, 0x00, 0xB1, 0x8F, 0x0B, 0xFC, 0x61, 0x05, 0x00, 0x00,
0x00, 0x09, 0x70, 0x48, 0x59, 0x73, 0x00, 0x00, 0x0E, 0xC3, 0x00, 0x00, 0x0E, 0xC3, 0x01, 0xC7,
0x6F, 0xA8, 0x64, 0x00, 0x00, 0x11, 0x54, 0x49, 0x44, 0x41, 0x54, 0x78, 0x5E, 0xED, 0x9C, 0x0D,
0x95, 0x23, 0xB9, 0x0E, 0x85, 0x9B, 0xC2, 0x50, 0x68, 0x0A, 0x43, 0xA1, 0x29, 0x34, 0x85, 0x50,
0x08, 0x85, 0x50, 0x08, 0x85, 0x50, 0x08, 0x85, 0x50, 0x08, 0x85, 0x50, 0xE8, 0xA7, 0x4F, 0x57,
0xB2, 0x65, 0x97, 0x93, 0xF4, 0xCC, 0xBE, 0xDD, 0xB7, 0x73, 0x5E, 0x6A, 0x8E, 0x46, 0xB6, 0xFE,
0x2C, 0x97, 0xAF, 0x55, 0xAE, 0xCA, 0xEC, 0xBE, 0x7D, 0xE3, 0xFA, 0xF1, 0xE2, 0x2F, 0xFE, 0x07,
0xF3, 0x5F, 0xBE, 0xDE, 0x8D, 0x3E, 0x8D, 0x76, 0x2F, 0x7A, 0xD1, 0x1F, 0x4E, 0x1F, 0x46, 0xE0,
0xF9, 0xE9, 0x66, 0xC0, 0xE0, 0xE3, 0xE7, 0xCF, 0x1F, 0x87, 0xCB, 0xF5, 0xF3, 0x7A, 0xBB, 0x7D,
0x7E, 0xDD, 0xBE, 0x76, 0x5F, 0xE2, 0x46, 0xAF, 0xFE, 0xAB, 0xFF, 0x87, 0xF5, 0x0D, 0xC7, 0xB7,
0x9F, 0x3F, 0xDF, 0x0F, 0xE0, 0xDA, 0x11, 0x7E, 0xE7, 0x72, 0xE0, 0x1F, 0x4F, 0x1F, 0x67, 0x39,
0x7D, 0x7C, 0x9D, 0x2E, 0x3F, 0x45, 0xE7, 0xE0, 0x73, 0xFB, 0xD5, 0x7F, 0xF5, 0xFF, 0xC5, 0xFD,
0xB3, 0x11, 0x38, 0x06, 0xCF, 0xE0, 0x1A, 0x7C, 0x07, 0xCE, 0x37, 0x17, 0xC0, 0xBF, 0x5C, 0xCD,
0x70, 0x08, 0x32, 0x73, 0xA3, 0xF3, 0x24, 0x7B, 0xF5, 0x5F, 0xFD, 0xFF, 0x65, 0x1F, 0x0E, 0xD0,
0x6B, 0x7F, 0xE0, 0x46, 0x6C, 0x02, 0xF0, 0x0D, 0xCE, 0x05, 0xF7, 0x7E, 0xBD, 0xFF, 0xFC, 0x78,
0x3F, 0xB0, 0x43, 0x4E, 0x97, 0xF7, 0xC9, 0xD9, 0xFA, 0x11, 0xE0, 0x74, 0x8E, 0x76, 0x95, 0xA7,
0xEC, 0x6E, 0xDB, 0x78, 0x4B, 0x62, 0xD5, 0x0E, 0xBB, 0xBB, 0xED, 0xE0, 0x77, 0xDB, 0xC6, 0x5F,
0xF1, 0x1F, 0xB4, 0x8D, 0xFF, 0xBF, 0xC6, 0x4F, 0xDB, 0xE0, 0xE0, 0x1B, 0x9C, 0x1B, 0xDE, 0x87,
0xEA, 0xFF, 0x71, 0xBE, 0x7C, 0x5C, 0xFD, 0xA8, 0xD3, 0x1C, 0x4A, 0xA0, 0x21, 0xC8, 0x36, 0xE8,
0x56, 0x5E, 0x93, 0x48, 0x7E, 0xCF, 0x2F, 0xF9, 0xCB, 0xBF, 0xF7, 0x5F, 0xFE, 0x8F, 0xF5, 0x2B,
0x7E, 0x4F, 0xDF, 0xE5, 0xE0, 0x1B, 0x9C, 0x83, 0x77, 0xC1, 0x5E, 0xD7, 0xCE, 0x8F, 0x3B, 0xC5,
0xF8, 0xE8, 0xCE, 0x99, 0x04, 0xBC, 0x06, 0xAF, 0xF2, 0xCA, 0xD1, 0x3F, 0xB2, 0x4B, 0xFD, 0x2C,
0x9F, 0xF5, 0xF0, 0x47, 0xFA, 0x59, 0x3E, 0xEB, 0x2D, 0x7F, 0x93, 0x1D, 0x4F, 0xEF, 0x5F, 0x87,
0xA3, 0x51, 0xE1, 0xC8, 0xD0, 0x8D, 0x63, 0x25, 0x27, 0xC6, 0x36, 0xD6, 0x56, 0x3F, 0xCB, 0x67,
0x3D, 0xFC, 0x91, 0x7E, 0x96, 0xCF, 0x7A, 0xF8, 0x23, 0xFD, 0x2C, 0x9F, 0xF5, 0xF0, 0x47, 0xFA,
0x59, 0x3E, 0xEB, 0xE1, 0x8F, 0xF4, 0xB3, 0x7C, 0xD6, 0xC3, 0x1F, 0xE9, 0x67, 0xF9, 0xAC, 0x87,
0x3F, 0xD2, 0xCF, 0xF2, 0x59, 0x5F, 0xF9, 0xA8, 0xBF, 0x5E, 0x3F, 0xBF, 0x0C, 0xEF, 0x7C, 0xC9,
0xF4, 0x8B, 0x47, 0xC0, 0xEE, 0x76, 0x53, 0xD5, 0x77, 0x60, 0xA4, 0x33, 0xFD, 0x74, 0x2C, 0xED,
0x04, 0x4F, 0x6E, 0x90, 0xA3, 0xEB, 0x8D, 0xC7, 0x60, 0x5D, 0x9F, 0xF6, 0xFF, 0x9C, 0x3E, 0x01,
0x7F, 0x3A, 0xEF, 0x6D, 0xA2, 0x67, 0x7B, 0xEB, 0xBF, 0x7D, 0xB5, 0xCB, 0x9A, 0x17, 0x93, 0x9D,
0xCE, 0x3B, 0xB7, 0x69, 0x71, 0xD2, 0x1F, 0x4E, 0x8C, 0x26, 0x37, 0xEE, 0xFD, 0xBF, 0x43, 0x4F,
0x15, 0xDA, 0x8D, 0xB6, 0x70, 0xD3, 0x63, 0x7B, 0xBE, 0xEC, 0xCD, 0x76, 0x37, 0xEA, 0xE0, 0x9B,
0x78, 0x7C, 0x9C, 0x38, 0x99, 0xFD, 0x61, 0xB4, 0x33, 0x8E, 0xEC, 0x7A, 0xBB, 0x7C, 0x5D, 0x2E,
0xC7, 0xA2, 0xC3, 0xD7, 0xF8, 0x66, 0x2D, 0xAB, 0xDE, 0x62, 0x47, 0x1E, 0x5B, 0xFB, 0x22, 0xBF,
0x80, 0x99, 0xB4, 0x49, 0x59, 0x89, 0x09, 0xDF, 0xE4, 0xFB, 0x77, 0xEB, 0x83, 0xD3, 0x37, 0x5D,
0x95, 0xE3, 0x73, 0x35, 0x9C, 0x83, 0xF7, 0xC0, 0xBD, 0x5F, 0x56, 0xF9, 0x35, 0x91, 0x21, 0x78,
0xBD, 0x41, 0x11, 0x88, 0x7E, 0x1D, 0x60, 0xDB, 0x97, 0x6D, 0xC6, 0x50, 0xFF, 0x9F, 0xD1, 0x7B,
0x55, 0x3F, 0xF1, 0x99, 0xEB, 0x1A, 0x68, 0xE7, 0x32, 0xF8, 0xDF, 0xD8, 0x00, 0x49, 0xBA, 0x6E,
0xB7, 0xAB, 0xD9, 0x7E, 0xF8, 0xD3, 0x00, 0xFF, 0x3E, 0x8F, 0xDE, 0x86, 0xAB, 0x8F, 0x0D, 0x63,
0x01, 0x58, 0x03, 0xA5, 0x71, 0x2D, 0x7C, 0x72, 0xEC, 0x6B, 0xBF, 0xF3, 0x6E, 0xAF, 0x38, 0x39,
0x8F, 0xB3, 0x01, 0x92, 0x0B, 0x70, 0xFA, 0x18, 0x1E, 0x5F, 0xE3, 0x43, 0x99, 0x73, 0x03, 0x75,
0xD3, 0x8B, 0xE7, 0x3D, 0x38, 0x9F, 0x0F, 0x6E, 0x77, 0xB9, 0x9C, 0x64, 0x57, 0x74, 0x6C, 0xF4,
0x36, 0x46, 0xCC, 0x73, 0x15, 0x0B, 0xBB, 0x9B, 0xD9, 0x9C, 0xCE, 0x9F, 0xA1, 0x1F, 0xE7, 0x20,
0xDE, 0x7D, 0xE1, 0xDC, 0x3F, 0x2E, 0xC5, 0x30, 0xAA, 0xF7, 0x0E, 0xDB, 0x18, 0x63, 0xDB, 0x97,
0x2C, 0xB1, 0x95, 0xB2, 0xFF, 0x9E, 0x3E, 0xB8, 0xF5, 0x87, 0x7C, 0x82, 0x53, 0xE4, 0xC1, 0xBB,
0x60, 0x1F, 0x95, 0x1F, 0xF0, 0xA7, 0xB1, 0x78, 0x77, 0x48, 0x99, 0x28, 0x83, 0xD5, 0x01, 0x42,
0xEE, 0x37, 0xA0, 0x0F, 0x3A, 0xDA, 0x3F, 0xD3, 0xF7, 0x78, 0x55, 0xF6, 0x5D, 0x7F, 0x07, 0xB1,
0x55, 0xF4, 0xBC, 0x58, 0x1C, 0x88, 0xAA, 0x77, 0x32, 0x00, 0x9E, 0xED, 0x49, 0x00, 0xE0, 0x5C,
0x5E, 0x36, 0x87, 0x3F, 0x05, 0xDC, 0x77, 0x1C, 0x4B, 0x63, 0x58, 0x3B, 0x6E, 0x2A, 0x7D, 0x80,
0x08, 0xD0, 0xF4, 0x34, 0x11, 0x69, 0x3B, 0xE5, 0xDF, 0x55, 0xDE, 0xDB, 0x8C, 0xDF, 0x63, 0x8B,
0x00, 0x2B, 0x36, 0xE7, 0xB3, 0x55, 0x65, 0x36, 0xAD, 0xC9, 0x72, 0x4E, 0xA9, 0xBB, 0x5E, 0x2F,
0xED, 0x09, 0xD5, 0xFC, 0x63, 0xD1, 0x33, 0x0E, 0x4F, 0x38, 0x2E, 0xE6, 0xA6, 0x38, 0x99, 0x3B,
0xF7, 0xC2, 0xA2, 0xF8, 0x26, 0x42, 0xCF, 0x26, 0xEA, 0x39, 0xD4, 0x18, 0x17, 0x1B, 0x27, 0x6D,
0xD9, 0x28, 0xCA, 0x9B, 0x8B, 0x2C, 0xD4, 0xBE, 0x52, 0x2C, 0xDC, 0x8F, 0xAF, 0x26, 0xEF, 0xB2,
0x33, 0xFB, 0xC3, 0x29, 0xC7, 0x0B, 0x3E, 0x61, 0x42, 0xF9, 0xCE, 0x63, 0x96, 0xFB, 0x5C, 0xE6,
0xB3, 0xD5, 0x1B, 0xB5, 0xFB, 0x5F, 0x7C, 0x8C, 0x37, 0xDB, 0x59, 0x1F, 0x18, 0x49, 0x7F, 0xB7,
0x6B, 0x39, 0x3C, 0xA9, 0xFC, 0x35, 0x70, 0xF6, 0xC7, 0x04, 0x15, 0xA4, 0xE9, 0x5C, 0x16, 0x89,
0x78, 0xBB, 0xF0, 0x61, 0xE2, 0x8F, 0xF4, 0x35, 0xD9, 0xE4, 0x35, 0xE9, 0x3B, 0xBC, 0xE8, 0xA9,
0xF8, 0x5C, 0x00, 0x1B, 0x80, 0x53, 0x71, 0x0F, 0x47, 0x8B, 0x6B, 0x1C, 0x30, 0x5D, 0xAE, 0xC7,
0x26, 0x83, 0x6B, 0x13, 0x68, 0x61, 0x79, 0x02, 0x24, 0x00, 0xFB, 0x18, 0x75, 0x8E, 0x31, 0x3F,
0xB3, 0x3B, 0x9F, 0xED, 0x88, 0x61, 0x74, 0x32, 0xD0, 0x5E, 0x2E, 0x56, 0x59, 0x0D, 0x00, 0xB4,
0x8F, 0x47, 0x03, 0xF8, 0x89, 0xA3, 0xD6, 0x05, 0xD1, 0xD7, 0xE9, 0x74, 0x30, 0xB9, 0x6C, 0x3D,
0x7E, 0xC4, 0x62, 0x5E, 0x6C, 0x36, 0x55, 0xE5, 0x9B, 0xFB, 0xD4, 0xB1, 0x8F, 0x06, 0x2E, 0x6D,
0xDC, 0xDB, 0xD7, 0xFE, 0x80, 0xDF, 0xCE, 0x6C, 0x19, 0x8F, 0x1C, 0xE6, 0xFB, 0x64, 0x6B, 0x66,
0xFE, 0xCC, 0x42, 0xE0, 0xEF, 0xE3, 0x64, 0xD5, 0x67, 0xFC, 0x8C, 0xA7, 0xCA, 0x5E, 0xD7, 0xD1,
0xC8, 0xC6, 0xDE, 0x1D, 0x7E, 0x58, 0x9F, 0xC2, 0xC0, 0x13, 0x44, 0x79, 0x91, 0xBF, 0xE7, 0x66,
0xF3, 0xF2, 0xBB, 0x6A, 0xF3, 0xCA, 0x8D, 0xA6, 0x23, 0x25, 0x05, 0x64, 0x0B, 0xFE, 0xDA, 0x1E,
0xEF, 0x63, 0xC8, 0x6C, 0xCD, 0xBA, 0x6E, 0xC1, 0x9B, 0x7E, 0xCC, 0x73, 0xAB, 0x5F, 0xF1, 0x7A,
0x7F, 0x6A, 0x3E, 0x5D, 0x57, 0xC0, 0xEF, 0x97, 0x2A, 0xFF, 0xB5, 0x1B, 0x77, 0xAA, 0x00, 0xE8,
0x03, 0xAD, 0xFA, 0xBD, 0x5D, 0x7D, 0xC6, 0x89, 0x27, 0xEF, 0x89, 0x8D, 0xF1, 0xBB, 0xBE, 0xCB,
0xAA, 0x6D, 0xF2, 0x31, 0xA6, 0xCE, 0xF8, 0xB9, 0xC0, 0x70, 0x36, 0x82, 0x16, 0xAA, 0x54, 0x30,
0x10, 0x19, 0x6D, 0x74, 0x00, 0xA5, 0xFA, 0xEC, 0xFD, 0x3D, 0xA1, 0xC6, 0x17, 0xD5, 0x36, 0xE3,
0xEC, 0x8E, 0x3F, 0xBE, 0x76, 0x7B, 0xD1, 0x39, 0x2A, 0x34, 0x20, 0x01, 0x40, 0xC8, 0x12, 0x14,
0xBB, 0xBD, 0x40, 0xB5, 0x37, 0xF2, 0x17, 0xED, 0x88, 0xC1, 0x7B, 0x08, 0x55, 0x34, 0xC7, 0x26,
0x06, 0xE0, 0x86, 0xD0, 0x37, 0xF0, 0xD9, 0x71, 0x06, 0xDF, 0xDC, 0x24, 0x80, 0x2F, 0x63, 0xB8,
0x9D, 0xD9, 0xEB, 0xCA, 0xB9, 0x69, 0x7E, 0x3A, 0xFF, 0x73, 0xA4, 0xC2, 0xE7, 0xFA, 0xF5, 0xB9,
0x7B, 0xB3, 0x58, 0xEA, 0x6B, 0x3C, 0x1D, 0xA3, 0xA0, 0xBC, 0xB7, 0xE4, 0xC7, 0x58, 0x9F, 0xFB,
0x37, 0x9F, 0x8B, 0x6F, 0x26, 0x36, 0xB7, 0x03, 0xBE, 0xC7, 0x42, 0x47, 0x1B, 0xEE, 0x9B, 0xDC,
0x7A, 0x14, 0x93, 0x1E, 0xAF, 0xE7, 0x57, 0xDB, 0xB5, 0x2F, 0x2E, 0x9F, 0xDA, 0x1E, 0x65, 0x5B,
0xDB, 0xAE, 0xEF, 0xFD, 0x51, 0x3F, 0xFA, 0xAE, 0xFA, 0xD0, 0xDD, 0xCA, 0x5F, 0x1D, 0x1E, 0x05,
0x52, 0xBB, 0x0F, 0x2A, 0x9A, 0x81, 0x3C, 0xD2, 0x23, 0x1D, 0xF4, 0x3B, 0x7A, 0x97, 0xD9, 0xC2,
0xF5, 0x47, 0xFC, 0xD5, 0xDA, 0x3A, 0xE2, 0x70, 0x01, 0x04, 0x16, 0x67, 0x6F, 0x80, 0x3D, 0x38,
0xF1, 0xB9, 0x4B, 0xA0, 0x49, 0x3B, 0x7C, 0xF0, 0x25, 0xC6, 0x58, 0x81, 0x3B, 0x0D, 0x63, 0x9B,
0x0D, 0x60, 0x39, 0x9C, 0x0C, 0xFC, 0x56, 0x55, 0x13, 0x0C, 0xC8, 0x19, 0x07, 0x60, 0x23, 0xDB,
0x03, 0x8A, 0xB0, 0xAD, 0x7E, 0xE8, 0x01, 0x8E, 0xF2, 0x15, 0xA8, 0x69, 0x03, 0xAE, 0x0E, 0xDA,
0x8B, 0x81, 0x91, 0x27, 0x84, 0xD1, 0xF1, 0xC3, 0x75, 0xC8, 0xD9, 0x08, 0x9E, 0xA3, 0x91, 0xF2,
0x67, 0xD3, 0xC8, 0xFF, 0x72, 0xB9, 0x38, 0x60, 0xB3, 0x82, 0x23, 0xF3, 0x23, 0x95, 0x6D, 0x22,
0xF2, 0xCB, 0x4D, 0xC5, 0xC5, 0x91, 0x85, 0xA7, 0x5F, 0xCE, 0x89, 0xFB, 0xE0, 0x73, 0xE2, 0x88,
0x15, 0x00, 0xEF, 0xA0, 0x07, 0xE4, 0xE2, 0xD2, 0xF1, 0xA4, 0x14, 0xF8, 0xD1, 0x27, 0xF8, 0xEF,
0xAD, 0xDF, 0xB3, 0x75, 0x85, 0xB6, 0x36, 0x23, 0xB6, 0x9E, 0xC5, 0x98, 0xF5, 0xBD, 0x3F, 0xE6,
0x06, 0xA7, 0xC8, 0x83, 0x77, 0xC1, 0xFE, 0x61, 0xE5, 0x7F, 0x4E, 0xF7, 0x12, 0xBB, 0x27, 0x87,
0xAA, 0x6E, 0x65, 0xF7, 0x2B, 0x7A, 0x16, 0x4C, 0x2F, 0x6C, 0x02, 0x15, 0x8B, 0xC8, 0xC5, 0x59,
0x9F, 0x6A, 0xEE, 0xA0, 0x33, 0x4E, 0x3B, 0x17, 0x98, 0x63, 0x10, 0x17, 0x6D, 0x80, 0x80, 0xAF,
0x36, 0x4A, 0x8F, 0x3B, 0x8F, 0xE3, 0xA0, 0x2B, 0x7D, 0x62, 0x51, 0xB5, 0x01, 0x40, 0x82, 0x9F,
0x0D, 0x91, 0x40, 0xAD, 0x15, 0xB1, 0x91, 0xD9, 0xE8, 0x69, 0xC0, 0x11, 0x4D, 0x47, 0x8C, 0x6C,
0x27, 0xC8, 0x2C, 0x19, 0xFE, 0x76, 0x59, 0xE3, 0x2E, 0x93, 0x8E, 0x0D, 0xC2, 0xD8, 0x6C, 0x34,
0x3D, 0x69, 0xE4, 0xD7, 0x80, 0xCF, 0xFB, 0x0C, 0xF6, 0xE9, 0x13, 0xC4, 0xDF, 0x8C, 0x97, 0xF2,
0xAB, 0xB5, 0x89, 0xC3, 0x7D, 0x42, 0xE7, 0x1B, 0xC2, 0x36, 0xC1, 0xD1, 0x08, 0xC1, 0xD1, 0x8E,
0x3D, 0x34, 0xB4, 0xC1, 0x4D, 0xDF, 0xC0, 0x9F, 0x95, 0xFF, 0xFE, 0x3C, 0x57, 0x6B, 0x96, 0xF4,
0x2B, 0x6B, 0xFB, 0x3B, 0xFA, 0x67, 0x74, 0xE9, 0xE0, 0xF7, 0xCA, 0xEF, 0xE0, 0x47, 0xE8, 0x06,
0x76, 0x43, 0xFE, 0x24, 0x0E, 0x08, 0x58, 0x6C, 0x2A, 0x2A, 0xC0, 0xA0, 0x22, 0x02, 0x66, 0x55,
0x4D, 0x1D, 0x39, 0x4E, 0x7E, 0x0C, 0xE2, 0xC5, 0x96, 0xEA, 0x0F, 0x48, 0xF9, 0xB5, 0x2F, 0x9E,
0x12, 0x2C, 0xBE, 0xB5, 0x21, 0x62, 0x79, 0xEC, 0xD5, 0x78, 0x4E, 0xBC, 0x17, 0x71, 0x9F, 0x78,
0x47, 0xE0, 0x67, 0xF6, 0x02, 0x7E, 0x93, 0x13, 0x37, 0x41, 0x41, 0xE5, 0x97, 0x2D, 0x3C, 0xCE,
0xE2, 0x31, 0x3E, 0x40, 0x05, 0x80, 0xA7, 0x13, 0xE7, 0x74, 0x8E, 0x19, 0x6A, 0x03, 0x30, 0x88,
0xB8, 0x00, 0xB9, 0x9E, 0xBB, 0x79, 0x7F, 0x00, 0xD8, 0xE4, 0xCC, 0x9C, 0x3C, 0xAF, 0xF6, 0x9E,
0x23, 0x90, 0x7A, 0x2C, 0xDF, 0x20, 0xB6, 0x91, 0x0F, 0x7C, 0xCE, 0x65, 0x53, 0xF2, 0x9E, 0xA3,
0x18, 0x3B, 0x7B, 0x87, 0xE0, 0x38, 0x86, 0xED, 0xDE, 0x9E, 0x28, 0xBA, 0x1F, 0x1F, 0x5E, 0x3C,
0x3C, 0x8A, 0xF9, 0x62, 0xAF, 0x39, 0x75, 0xF0, 0xFB, 0x66, 0x09, 0xF0, 0x9B, 0x89, 0xF3, 0xDC,
0x74, 0x0D, 0xFC, 0xF3, 0xFD, 0xFA, 0x97, 0xF2, 0x52, 0xF9, 0xFB, 0xB1, 0xE7, 0x72, 0xDD, 0x1A,
0x3E, 0xE7, 0x01, 0x16, 0xEF, 0x5B, 0x7B, 0x29, 0x9F, 0xFB, 0x8F, 0xEC, 0x1E, 0xF1, 0xB5, 0x1F,
0x80, 0x05, 0xB8, 0x0E, 0x7E, 0x03, 0x06, 0x2F, 0xB6, 0x2C, 0x0C, 0x67, 0x58, 0x6C, 0xBC, 0xD2,
0x5B, 0x55, 0xA7, 0xDA, 0xB3, 0x19, 0xD2, 0x07, 0x1B, 0xAA, 0x1E, 0x8F, 0x7F, 0x00, 0xCB, 0xC2,
0x3B, 0xF8, 0x73, 0x8C, 0x32, 0x9E, 0x57, 0x48, 0x8E, 0x4B, 0x94, 0x48, 0x96, 0x1C, 0x04, 0xF8,
0xD2, 0x8B, 0xFB, 0x65, 0x32, 0xA9, 0xBB, 0xBC, 0xF6, 0xFD, 0x68, 0xE1, 0xB1, 0xFA, 0x39, 0xFE,
0x70, 0xFC, 0x74, 0x70, 0x79, 0xDB, 0x00, 0xF5, 0x69, 0x95, 0x3C, 0x29, 0xDF, 0x21, 0x9C, 0x47,
0x1B, 0xB9, 0xCF, 0xCB, 0x62, 0x10, 0xCB, 0x2B, 0xB9, 0xFD, 0xF1, 0x31, 0x8C, 0x78, 0x9F, 0x00,
0xDC, 0x87, 0x23, 0x47, 0x38, 0xC8, 0xE6, 0xE6, 0xF7, 0xE5, 0x68, 0x31, 0xDE, 0xDC, 0x9E, 0xB1,
0x01, 0x3C, 0x73, 0x65, 0x5E, 0xDC, 0x13, 0xE2, 0xE2, 0x93, 0x9B, 0x8D, 0x7C, 0xD8, 0x8C, 0xCA,
0x7B, 0x51, 0xF9, 0x6D, 0x23, 0xD1, 0xE7, 0xF2, 0xE3, 0x5D, 0x5D, 0x1B, 0x28, 0xDB, 0xBF, 0xC5,
0x4B, 0xAC, 0x6F, 0xE1, 0xA6, 0xCA, 0x57, 0x7C, 0xD4, 0x97, 0xCA, 0xEF, 0x57, 0x54, 0x7E, 0x19,
0xB0, 0x38, 0x32, 0xE4, 0x06, 0xA7, 0x63, 0xE7, 0x5E, 0x75, 0x4A, 0xC0, 0x6E, 0x6F, 0xED, 0xA5,
0xBC, 0xEB, 0xD3, 0xEF, 0x9E, 0x5D, 0x1D, 0xAF, 0x8F, 0x83, 0x1C, 0x7D, 0x95, 0xD3, 0x97, 0x1C,
0x30, 0x38, 0xF8, 0x0D, 0xC0, 0x00, 0x99, 0xEF, 0xDE, 0x00, 0x35, 0x81, 0x0C, 0x67, 0xC1, 0xF8,
0x53, 0x65, 0xD8, 0xF0, 0xF2, 0x87, 0x3D, 0x00, 0xB9, 0xB1, 0x61, 0x42, 0x9F, 0x39, 0x67, 0x0E,
0xE4, 0xA8, 0x33, 0x36, 0x4F, 0x16, 0xBE, 0xE0, 0xD8, 0xA6, 0x31, 0xEE, 0x7E, 0x06, 0x3A, 0x40,
0x45, 0xA5, 0x46, 0x8E, 0x0C, 0x30, 0xAA, 0x1F, 0xB6, 0xBE, 0xF1, 0x78, 0x5A, 0x58, 0x4C, 0x1B,
0x43, 0x40, 0xBD, 0x1A, 0x50, 0xDF, 0x5A, 0x8C, 0xFD, 0x81, 0x63, 0x94, 0x01, 0xCE, 0xE4, 0x79,
0x14, 0x1B, 0xB8, 0xCF, 0x8F, 0x23, 0x8F, 0x72, 0xD4, 0xD3, 0x2B, 0x37, 0x99, 0xFE, 0x38, 0x68,
0x6D, 0x3C, 0x2E, 0x7C, 0xF8, 0x12, 0x84, 0x8C, 0xF1, 0x99, 0x5B, 0xDA, 0x23, 0xEF, 0x71, 0x94,
0x13, 0x7A, 0x36, 0x19, 0x2F, 0xDA, 0xC4, 0x4A, 0xD0, 0xE3, 0xAB, 0x7C, 0xA3, 0xF2, 0x9B, 0x6C,
0xAE, 0xFC, 0xFD, 0x3E, 0xD5, 0x7B, 0x27, 0x9E, 0xFD, 0xBE, 0x8E, 0x5B, 0xFD, 0x7A, 0xDD, 0xCD,
0x66, 0xB0, 0x7F, 0x2E, 0x9F, 0xFD, 0x3B, 0x37, 0xF2, 0x71, 0x00, 0xFF, 0xFB, 0xBA, 0xF2, 0xB7,
0xE4, 0x8D, 0xB7, 0x84, 0xC2, 0xC9, 0x79, 0x09, 0x38, 0x24, 0xFF, 0x44, 0x0E, 0x4F, 0xB9, 0x73,
0xFA, 0x8B, 0xF8, 0x6B, 0xF9, 0x63, 0x1B, 0x16, 0x8D, 0x85, 0x80, 0xA8, 0xEE, 0xF4, 0xD9, 0x10,
0x8C, 0xA3, 0xB1, 0x3E, 0x1D, 0x68, 0x80, 0x88, 0x2F, 0x3C, 0x9E, 0x4B, 0x2C, 0x36, 0x84, 0x8F,
0xFB, 0x1A, 0xE9, 0x69, 0x81, 0x5F, 0x8C, 0x55, 0xE6, 0x81, 0xAE, 0x56, 0x60, 0xBE, 0x8C, 0x10,
0x13, 0x60, 0x50, 0x35, 0x53, 0x96, 0xC7, 0x9E, 0x4F, 0xBE, 0xF6, 0x84, 0x2D, 0x7E, 0x8C, 0xA9,
0x9C, 0x0C, 0xB4, 0x0E, 0x21, 0x40, 0xD9, 0xBF, 0xE4, 0x60, 0x93, 0xEF, 0x10, 0x39, 0x1F, 0x62,
0xC1, 0x7D, 0x1C, 0xEC, 0x0D, 0xD8, 0x39, 0xAF, 0xFC, 0xF5, 0x1A, 0xBD, 0x19, 0x78, 0x2C, 0x72,
0x71, 0xF0, 0x46, 0x5E, 0x7C, 0x2A, 0xE5, 0xC2, 0x86, 0x27, 0x81, 0xDA, 0x3A, 0xDE, 0xF9, 0xBD,
0x29, 0xF3, 0xA3, 0x10, 0x10, 0xD3, 0x73, 0xF2, 0x78, 0xCA, 0x85, 0x27, 0x80, 0x72, 0xB2, 0xFB,
0x67, 0x15, 0x9F, 0x36, 0xC7, 0x28, 0x1F, 0xD7, 0x2E, 0xBD, 0x57, 0xC5, 0x7A, 0x4C, 0xF7, 0x4E,
0xF7, 0xFF, 0x57, 0xD6, 0x3A, 0xE5, 0xDF, 0xB1, 0xD9, 0xCA, 0x7D, 0xEC, 0x90, 0xE7, 0xFC, 0x74,
0xBF, 0xBA, 0x7C, 0x06, 0xBF, 0x2A, 0xFF, 0x65, 0x74, 0xC8, 0x01, 0x72, 0x32, 0x39, 0x98, 0x07,
0xFB, 0x46, 0x7B, 0x88, 0xE3, 0xFD, 0x1A, 0x2B, 0x93, 0xAD, 0x6D, 0xB3, 0x1B, 0x62, 0x55, 0x9B,
0x2A, 0x1F, 0x6D, 0x58, 0x48, 0xAF, 0xB8, 0x80, 0xC4, 0xCE, 0xC4, 0x09, 0x7C, 0x6C, 0x2A, 0xB8,
0x21, 0xAF, 0x9C, 0xBE, 0xF0, 0xF2, 0xC7, 0x56, 0x0B, 0x2B, 0x1D, 0xF6, 0x99, 0x9F, 0xC7, 0x8F,
0x38, 0xD9, 0x6E, 0xE4, 0x76, 0x02, 0x02, 0x20, 0xE3, 0x48, 0xE1, 0x1B, 0xCA, 0xE2, 0x65, 0xE5,
0xE7, 0x38, 0xD0, 0x01, 0x9F, 0xA4, 0x9C, 0xD8, 0x2C, 0x02, 0x8F, 0xFC, 0xA9, 0xD6, 0xF8, 0x3A,
0xE0, 0xAC, 0x0F, 0xB8, 0xD8, 0x48, 0x7C, 0xA6, 0x64, 0xF3, 0x78, 0x35, 0x37, 0x39, 0x63, 0x12,
0x07, 0x20, 0xE3, 0x07, 0x01, 0x48, 0xB6, 0x12, 0xF7, 0x20, 0xED, 0xF9, 0xF2, 0x43, 0xEC, 0xBD,
0x1D, 0xAB, 0x72, 0x23, 0x50, 0xC9, 0xD9, 0x2C, 0xF9, 0x09, 0x55, 0x71, 0x6C, 0x6E, 0xBE, 0x2E,
0x3C, 0x09, 0xB5, 0x89, 0xF2, 0xEB, 0x91, 0xDF, 0x4F, 0xEB, 0x27, 0xD0, 0xC9, 0x8D, 0xA3, 0x19,
0xBA, 0xAC, 0xFC, 0x1A, 0x83, 0xCA, 0xAF, 0xB9, 0xD5, 0x7B, 0x97, 0xED, 0x71, 0x6D, 0xA3, 0x8D,
0xDE, 0xC7, 0xCD, 0xB6, 0xB8, 0xC7, 0x88, 0x7C, 0x9E, 0xB7, 0xC7, 0x38, 0x1A, 0x8F, 0x31, 0x1E,
0xCB, 0xCF, 0x86, 0xF3, 0x00, 0xBF, 0x5F, 0x0E, 0xFE, 0xB3, 0x57, 0x7E, 0x1C, 0x3A, 0x29, 0x59,
0x28, 0x82, 0xB8, 0xAC, 0xDB, 0x0D, 0x93, 0xD9, 0xB4, 0x3B, 0x6D, 0x7D, 0xA0, 0x1E, 0x73, 0x94,
0xCF, 0xED, 0xEA, 0xBB, 0x88, 0x03, 0x10, 0xCF, 0xAA, 0xEE, 0x10, 0x15, 0x2C, 0x2B, 0x1B, 0x9C,
0x9F, 0xED, 0x59, 0x28, 0x27, 0x6B, 0xA7, 0x0E, 0x3F, 0x55, 0x3B, 0x55, 0x57, 0x9E, 0x10, 0x09,
0x56, 0xC5, 0xEE, 0x37, 0xAE, 0xE7, 0xDA, 0xC7, 0xD7, 0x17, 0xA6, 0xF8, 0x62, 0x53, 0xC6, 0x23,
0x1E, 0x20, 0xD3, 0xD7, 0x25, 0xC9, 0xE7, 0xB9, 0x52, 0xE5, 0xF5, 0x95, 0x07, 0xF8, 0x00, 0x62,
0x7B, 0x41, 0xE7, 0xFC, 0xCF, 0xB1, 0xC7, 0xFE, 0x00, 0x2E, 0x62, 0x39, 0xB1, 0x29, 0x02, 0xB8,
0x09, 0x7E, 0xE9, 0xF8, 0x0F, 0x34, 0xEC, 0x8C, 0x1E, 0xD5, 0x98, 0xE3, 0x58, 0xB3, 0xB7, 0x38,
0x5C, 0xE8, 0x00, 0xAD, 0xBF, 0x4C, 0xDB, 0xDC, 0x01, 0x37, 0xE3, 0xF4, 0xBC, 0x74, 0x4F, 0xF1,
0xD3, 0x66, 0xE2, 0x77, 0x81, 0x77, 0x7F, 0x6A, 0xE5, 0xD3, 0xC6, 0xDF, 0x05, 0xD8, 0x58, 0xFC,
0x06, 0xE0, 0x9B, 0x96, 0x27, 0x28, 0xF7, 0xAD, 0x1E, 0x7B, 0xFA, 0x7A, 0xCC, 0xF7, 0x69, 0x96,
0x6D, 0xDB, 0xD5, 0x46, 0xBA, 0x51, 0xDE, 0x63, 0x6D, 0xD7, 0xBF, 0x8E, 0x53, 0xFD, 0xEE, 0xFB,
0x80, 0x73, 0xF0, 0x1E, 0xB8, 0xF7, 0x6B, 0x77, 0xBE, 0x6C, 0x8D, 0xE7, 0x44, 0x56, 0x34, 0x26,
0x5A, 0x75, 0xE1, 0xEB, 0xE0, 0xE9, 0xB2, 0x6C, 0x77, 0xDB, 0xE7, 0x49, 0xAB, 0xDF, 0xDB, 0x69,
0x97, 0x32, 0x16, 0x3C, 0x2B, 0xBC, 0x2A, 0x93, 0x80, 0xCC, 0xA2, 0xEA, 0x1D, 0x40, 0xE0, 0xA7,
0xEA, 0xA5, 0x3C, 0xBF, 0x94, 0xA0, 0xC3, 0xD7, 0xAB, 0x77, 0x8B, 0xFF, 0x98, 0x12, 0x5C, 0x00,
0x84, 0xA3, 0x05, 0xF1, 0x90, 0xC3, 0x33, 0x87, 0xAC, 0x88, 0xAB, 0xBC, 0xC9, 0xC1, 0xC1, 0x13,
0x9B, 0x07, 0xFB, 0xDA, 0x06, 0x6C, 0x7A, 0xC7, 0xE0, 0x57, 0x62, 0x5E, 0xE2, 0x25, 0x77, 0xF0,
0x33, 0x96, 0x11, 0x73, 0x26, 0x67, 0x64, 0xE8, 0xA8, 0xFC, 0x8C, 0xAF, 0xB9, 0xF1, 0x75, 0xE8,
0x60, 0x7A, 0x1D, 0xBD, 0xD8, 0x28, 0xD8, 0xF0, 0x44, 0xE8, 0xF3, 0xEC, 0xF7, 0x97, 0x71, 0x00,
0x32, 0x1B, 0x05, 0x3D, 0xB1, 0x2C, 0x1D, 0xCF, 0x27, 0xE7, 0x43, 0x1B, 0x1D, 0xC4, 0xD3, 0x45,
0x72, 0x3B, 0xF6, 0xD8, 0x18, 0x19, 0x67, 0x5C, 0xBF, 0x51, 0x36, 0xDE, 0x87, 0xEC, 0x8F, 0xBE,
0xD5, 0x66, 0x6D, 0xBF, 0x96, 0x7D, 0xC7, 0x36, 0xC7, 0x2A, 0x95, 0xBF, 0x1F, 0x7B, 0x10, 0x56,
0xE3, 0x75, 0x00, 0xD1, 0x23, 0xDD, 0x23, 0xFA, 0x8E, 0xDF, 0xEF, 0xD8, 0x38, 0xF8, 0x0C, 0x6C,
0x09, 0x72, 0x2E, 0x40, 0x23, 0x80, 0x60, 0x4F, 0x95, 0xFC, 0xF4, 0xB6, 0x2A, 0x23, 0x9F, 0xF1,
0x74, 0x16, 0x66, 0x51, 0xF1, 0x4D, 0x00, 0x27, 0xD5, 0x31, 0x6A, 0x9B, 0x8D, 0xC2, 0x31, 0x03,
0x74, 0x1C, 0x0D, 0x24, 0x1D, 0x4C, 0xCA, 0x83, 0xCA, 0x08, 0x58, 0x46, 0x50, 0x88, 0x32, 0x0E,
0x63, 0x02, 0x36, 0xC6, 0xFF, 0xF0, 0x5F, 0x60, 0x79, 0xF9, 0xD6, 0xE7, 0xCD, 0x08, 0xEE, 0x7F,
0x92, 0xB3, 0x31, 0xB8, 0xFC, 0x89, 0x50, 0xE2, 0x25, 0xD0, 0xB9, 0xF0, 0xF5, 0x27, 0x87, 0x6D,
0x48, 0xE2, 0xFA, 0x13, 0xAD, 0xB5, 0x55, 0xA5, 0xA5, 0x93, 0x1C, 0x5B, 0x8F, 0xE3, 0x39, 0x4B,
0xCF, 0x93, 0x22, 0xFF, 0xB9, 0x04, 0xB6, 0xBB, 0xC3, 0x4F, 0xDB, 0x3C, 0x7C, 0xBE, 0x95, 0x1E,
0x1F, 0xC6, 0xE4, 0x1E, 0xA6, 0xAC, 0x1F, 0x7B, 0xD6, 0xF4, 0x3B, 0xEB, 0xF9, 0x8C, 0x7E, 0xC5,
0x7E, 0xB6, 0xA5, 0xC8, 0x07, 0xF8, 0xDB, 0xD5, 0x2A, 0xFF, 0x1F, 0x49, 0x2C, 0x88, 0x91, 0x7F,
0xB3, 0xB6, 0xEA, 0xC6, 0xC2, 0xE8, 0xA2, 0x62, 0x01, 0x2A, 0xAA, 0x28, 0x15, 0x54, 0x5F, 0x2E,
0x5C, 0x6E, 0x36, 0x02, 0x7E, 0xAF, 0xDC, 0x0F, 0x09, 0x90, 0x18, 0x20, 0x2C, 0x80, 0x2F, 0x3A,
0x9F, 0x05, 0xF9, 0x5A, 0x53, 0x7D, 0x1D, 0x14, 0x7E, 0xBC, 0x00, 0xA8, 0xF1, 0xBB, 0x42, 0x25,
0xB3, 0xAD, 0xDF, 0xD4, 0xF5, 0xDD, 0xDD, 0xAA, 0x29, 0xE7, 0x7B, 0xAB, 0xA6, 0xED, 0x5C, 0x6E,
0xB1, 0xF3, 0xDB, 0x3C, 0x3C, 0x37, 0x0B, 0x15, 0x7A, 0xC8, 0xD5, 0xDA, 0x59, 0xF9, 0xD9, 0x40,
0x54, 0x7B, 0xE6, 0xBE, 0xA5, 0xBE, 0x09, 0x92, 0xF4, 0xA4, 0xB0, 0x8D, 0x47, 0x21, 0x88, 0x39,
0x71, 0xC4, 0xF1, 0xDC, 0x88, 0x67, 0x47, 0x25, 0x55, 0x7A, 0xE6, 0xC5, 0xB1, 0x92, 0x7F, 0x64,
0xA7, 0x22, 0x92, 0x47, 0x22, 0xE6, 0xB0, 0x2F, 0x9B, 0xFF, 0x4F, 0xA0, 0x02, 0xFE, 0x5E, 0xF9,
0x4F, 0x79, 0x3C, 0x99, 0x6E, 0x6E, 0x6B, 0x7F, 0xA7, 0x3F, 0xCB, 0xB2, 0xBD, 0x92, 0xCD, 0xED,
0xBF, 0xD0, 0xF7, 0x6A, 0x68, 0x0B, 0xC9, 0x42, 0xF9, 0x8F, 0x48, 0x06, 0xF8, 0xDC, 0x04, 0xBC,
0x04, 0xEB, 0xD2, 0x62, 0x21, 0x43, 0xA7, 0x5F, 0x80, 0xAD, 0xE2, 0xB3, 0x78, 0x19, 0xB7, 0xC6,
0x9F, 0xC6, 0x02, 0x74, 0x1C, 0x23, 0xF0, 0x3F, 0x1A, 0x30, 0x38, 0x03, 0x37, 0x20, 0x06, 0x07,
0x4C, 0x7C, 0x2D, 0x71, 0xA0, 0x00, 0xFE, 0x8D, 0x1E, 0xB0, 0xEE, 0x7C, 0x7C, 0x8E, 0x4B, 0xCA,
0x57, 0xF9, 0x93, 0x07, 0xD5, 0x5B, 0x20, 0xE3, 0xEB, 0x91, 0xE6, 0x03, 0xCF, 0x1F, 0xC0, 0xD0,
0x33, 0x4F, 0x8F, 0x19, 0xE3, 0x65, 0xE5, 0xF7, 0x17, 0xDE, 0xD8, 0x44, 0x7A, 0xF1, 0x7D, 0xB3,
0xA7, 0x8A, 0xFC, 0xE9, 0xF3, 0x84, 0xF9, 0xB4, 0xFE, 0x87, 0xDB, 0x04, 0xA8, 0x6D, 0x6C, 0x00,
0xEE, 0x4F, 0x0D, 0xDB, 0x3C, 0xC8, 0x39, 0xCF, 0xD3, 0x6E, 0x1B, 0x3B, 0x6C, 0x7C, 0x93, 0xBA,
0x8F, 0xE6, 0xC8, 0x1C, 0xD8, 0xA4, 0x1B, 0xF0, 0xE3, 0x53, 0xFB, 0xBF, 0x2A, 0xFF, 0x3B, 0xFB,
0xD6, 0x06, 0xE7, 0x77, 0xC1, 0xBF, 0x3F, 0x25, 0x18, 0x6C, 0xF2, 0xCD, 0xB9, 0x00, 0x24, 0x64,
0xED, 0xF1, 0x9B, 0xF2, 0x49, 0xEF, 0x3E, 0xB5, 0xDD, 0xF4, 0x73, 0xAC, 0xD1, 0x6E, 0x18, 0xB3,
0xB5, 0xE1, 0xD1, 0x37, 0x12, 0xE8, 0x66, 0xBD, 0x72, 0xCF, 0xB3, 0x30, 0x15, 0x9D, 0x4A, 0x48,
0xE5, 0x77, 0xB0, 0x5B, 0xC5, 0xD5, 0x53, 0x80, 0xAA, 0x0A, 0xF0, 0x64, 0x9B, 0x00, 0xF6, 0x79,
0xD7, 0x78, 0xC8, 0x6A, 0x6C, 0xC8, 0x7C, 0x1C, 0x38, 0x05, 0x18, 0xB2, 0x65, 0x3C, 0xD9, 0xA8,
0x8A, 0x67, 0xEC, 0x9E, 0xB3, 0xCF, 0x0D, 0x7D, 0xC6, 0x30, 0x7D, 0x9D, 0x7B, 0xD5, 0xB5, 0xBC,
0x4C, 0x3F, 0xFB, 0x48, 0x4E, 0x3C, 0x71, 0x74, 0xFC, 0xB0, 0xE5, 0x7E, 0xD6, 0x1F, 0xC8, 0x74,
0xA3, 0x6C, 0x8C, 0x5D, 0x73, 0xF6, 0xD8, 0x46, 0x8A, 0xD7, 0x6D, 0x72, 0x3D, 0xDC, 0xDF, 0xDA,
0xC4, 0xCC, 0x39, 0x62, 0xEB, 0x76, 0x61, 0x33, 0xAE, 0x89, 0xE2, 0xBB, 0xEF, 0x1D, 0x79, 0xDA,
0x2B, 0x46, 0xC8, 0xB1, 0x1B, 0xDA, 0x46, 0xD9, 0x0E, 0xDE, 0x31, 0x32, 0xCA, 0x97, 0x32, 0x6F,
0xC7, 0xF8, 0x46, 0x05, 0xFC, 0xED, 0x32, 0xF0, 0x87, 0x81, 0x39, 0xF5, 0x0D, 0x40, 0x62, 0x92,
0xA7, 0x4C, 0x3C, 0x6D, 0xF3, 0xE6, 0x54, 0x9E, 0x76, 0x45, 0x6E, 0xB4, 0x94, 0x6F, 0x38, 0xB6,
0xD6, 0x9E, 0xC7, 0x6E, 0x5C, 0xFA, 0x6D, 0xFC, 0xF0, 0x41, 0x8E, 0x0C, 0xCE, 0x22, 0x55, 0x6A,
0x0B, 0x6C, 0x7A, 0xC8, 0xED, 0xC3, 0x9F, 0xF8, 0xF4, 0xDB, 0xD8, 0x70, 0x8D, 0xE9, 0x3A, 0xC6,
0x8C, 0xF1, 0x93, 0x7A, 0x1E, 0xC9, 0x65, 0x2B, 0x7D, 0xC6, 0x51, 0xBB, 0xE6, 0xEE, 0xDC, 0x6D,
0x4A, 0xBF, 0xC5, 0xEF, 0xFE, 0xCA, 0xA7, 0xCA, 0xAA, 0xDC, 0x64, 0x39, 0x6E, 0xC8, 0x3D, 0x0E,
0xFA, 0xCC, 0x03, 0x5E, 0xDB, 0xA9, 0x6B, 0x63, 0x9A, 0x5F, 0x8B, 0x21, 0xD9, 0xC0, 0x8B, 0x7F,
0xFA, 0xB9, 0x2D, 0x7E, 0x6E, 0x23, 0x3B, 0xC9, 0xBA, 0xAE, 0x8F, 0x17, 0xF2, 0xDA, 0x4E, 0x1B,
0x6B, 0x0F, 0x36, 0xC6, 0x87, 0x7C, 0x5C, 0x97, 0x36, 0x5B, 0xEE, 0xB6, 0xCE, 0x4D, 0xE6, 0xBE,
0x95, 0xAF, 0xF4, 0xD9, 0xB7, 0x27, 0xEF, 0xAA, 0xF2, 0xFB, 0x77, 0x51, 0x33, 0xD8, 0x26, 0x15,
0x01, 0x8A, 0xEC, 0x71, 0x72, 0xD5, 0x2E, 0x38, 0x03, 0xAF, 0xE4, 0x77, 0xFC, 0xB7, 0x93, 0x8A,
0x18, 0x85, 0x0F, 0x7E, 0x61, 0x2B, 0x7D, 0xF7, 0x73, 0x8A, 0x4A, 0x87, 0x5F, 0x56, 0xE3, 0xD4,
0x3D, 0x8E, 0x6F, 0x6D, 0x62, 0x3A, 0xAF, 0xF2, 0xD1, 0x2E, 0xE3, 0xAC, 0xE6, 0x35, 0xC6, 0xEB,
0x63, 0x0D, 0xB9, 0x3B, 0xAF, 0x76, 0xD8, 0xA4, 0xAD, 0xE2, 0x55, 0x7E, 0x2F, 0xDF, 0xB4, 0x1F,
0xF8, 0xC3, 0x71, 0x7A, 0x2C, 0xDA, 0xE2, 0xF4, 0x63, 0xAC, 0xC9, 0xB7, 0xFA, 0x75, 0x9D, 0xC9,
0xC3, 0xFE, 0x19, 0xBF, 0x9F, 0x5B, 0xB1, 0x8D, 0x31, 0xBE, 0xCB, 0x57, 0xE3, 0xD4, 0x9C, 0x57,
0xF2, 0x02, 0xFE, 0x76, 0xED, 0x78, 0x2B, 0x96, 0x61, 0x3A, 0x2A, 0xC9, 0x4D, 0xF0, 0x18, 0xD8,
0xF9, 0x77, 0xF5, 0xD9, 0xDF, 0xF8, 0x95, 0xB1, 0x92, 0x0F, 0x6D, 0xE3, 0x66, 0xB7, 0xF2, 0x1D,
0x6F, 0xA0, 0xFA, 0x2D, 0x66, 0xEA, 0xDC, 0x3E, 0x74, 0xB3, 0x4D, 0x19, 0x6F, 0xCE, 0xCF, 0xFD,
0x63, 0x8C, 0xB4, 0xEF, 0xE3, 0xAE, 0x7D, 0x9B, 0xAC, 0xE9, 0xD2, 0x77, 0xD6, 0x4B, 0x37, 0x82,
0x61, 0x1C, 0xBB, 0xF9, 0x85, 0x4D, 0xFA, 0xBB, 0x5D, 0xD8, 0x8C, 0xFE, 0x19, 0x3B, 0xF8, 0x37,
0xF4, 0x1A, 0x43, 0x32, 0xC5, 0xEF, 0xF2, 0xEA, 0xD7, 0x72, 0x31, 0x9B, 0xCD, 0x5C, 0x8B, 0x2C,
0xF3, 0x1D, 0x78, 0xF5, 0xB1, 0xFE, 0x3A, 0xA7, 0xE7, 0xBE, 0xF7, 0xF4, 0x4B, 0xDB, 0x95, 0x8F,
0xCB, 0xD5, 0x36, 0x7E, 0x33, 0xBC, 0xF3, 0x1F, 0xB0, 0x7B, 0xE5, 0xE7, 0xFA, 0xB0, 0x23, 0xC1,
0x19, 0xC7, 0x9C, 0xD4, 0x30, 0xC1, 0x12, 0xC0, 0x6F, 0xC8, 0xDC, 0x76, 0xBB, 0x1C, 0x2C, 0x7C,
0x6A, 0x12, 0x85, 0x0F, 0x0B, 0x30, 0xEB, 0xA7, 0x64, 0xDB, 0x0D, 0x4A, 0x5E, 0x65, 0xC5, 0xB6,
0xC6, 0x6C, 0x79, 0xA5, 0xDE, 0xF9, 0x64, 0x13, 0x7C, 0x88, 0x91, 0xF2, 0xC5, 0xBC, 0x91, 0xD5,
0x7E, 0x8F, 0x9B, 0xFD, 0xE2, 0x0F, 0xD5, 0x18, 0xCD, 0xB7, 0xDA, 0x2C, 0xC6, 0x40, 0xE6, 0xB9,
0x4F, 0x71, 0x23, 0x56, 0xF3, 0x73, 0x9B, 0x31, 0x66, 0x9B, 0xE3, 0xD4, 0x16, 0xB7, 0x7E, 0xE4,
0x50, 0xDB, 0xC3, 0x5C, 0x82, 0xCF, 0xF6, 0x43, 0x9C, 0xD9, 0xAF, 0xF6, 0x7F, 0x67, 0x4C, 0xF7,
0xC9, 0xFE, 0xE8, 0xDB, 0xEF, 0x9F, 0x71, 0x9F, 0xEF, 0x64, 0xBB, 0xD1, 0x4F, 0x71, 0xAA, 0x4F,
0x1D, 0xC7, 0xFC, 0x0C, 0xE7, 0x9B, 0xFF, 0x71, 0xD5, 0xFB, 0xFB, 0xFB, 0xDB, 0xFE, 0x68, 0xE7,
0xFE, 0x39, 0x10, 0xCE, 0xF4, 0x47, 0xB9, 0xFA, 0x4A, 0xA4, 0xB4, 0x43, 0x2E, 0x1E, 0x94, 0xFE,
0x9E, 0x44, 0x97, 0x67, 0x82, 0x3D, 0x7E, 0xA5, 0x12, 0xA3, 0xE9, 0xD7, 0x63, 0x0C, 0x31, 0x97,
0xED, 0xC9, 0x36, 0xE2, 0x0D, 0xB6, 0x9B, 0x1C, 0xCC, 0x67, 0xB2, 0xAB, 0xBE, 0x03, 0xE5, 0x22,
0xBB, 0xAE, 0xE6, 0xA8, 0x3E, 0x5C, 0xBE, 0xD5, 0x6E, 0xEC, 0x77, 0x9F, 0xF1, 0x9E, 0x36, 0xDF,
0x92, 0x4F, 0x93, 0x85, 0x7F, 0xD3, 0x4F, 0xED, 0x1A, 0x43, 0xBA, 0xDE, 0xEE, 0x39, 0x84, 0xAE,
0xF5, 0x8B, 0xCD, 0x6A, 0x4C, 0xA8, 0xC8, 0x3A, 0x7D, 0x63, 0xCC, 0xA6, 0x9F, 0xE8, 0xE1, 0xFD,
0x93, 0xAC, 0xEA, 0x47, 0x9D, 0xA8, 0xDE, 0x0F, 0xD1, 0xC2, 0xCE, 0xFC, 0x8F, 0x46, 0xEF, 0xEF,
0x3F, 0xF6, 0x86, 0xF7, 0x56, 0xF5, 0xF3, 0xFA, 0xF8, 0xDC, 0xFD, 0x38, 0xF9, 0x8D, 0xF0, 0x81,
0x44, 0x99, 0xCC, 0xFA, 0x06, 0x15, 0x0A, 0x7D, 0x26, 0x22, 0xBF, 0xCE, 0x65, 0xB7, 0xE5, 0xCD,
0xCE, 0xFD, 0xA3, 0x6F, 0xED, 0xD1, 0x5F, 0x32, 0xF9, 0x6C, 0x73, 0xC9, 0x1D, 0xDE, 0xE2, 0x96,
0x1C, 0x33, 0xAE, 0xFA, 0x85, 0x2F, 0xE4, 0x63, 0xC5, 0x5B, 0xDB, 0x29, 0xBF, 0x9E, 0xD7, 0xCC,
0xD3, 0x5E, 0x76, 0x21, 0x0B, 0x92, 0xCD, 0xEC, 0x1B, 0xB1, 0x57, 0xB6, 0x83, 0xAC, 0x8F, 0x5D,
0xED, 0x06, 0x9B, 0x68, 0x0F, 0xF1, 0x27, 0x7D, 0xAB, 0x82, 0x39, 0x6E, 0xE3, 0xA2, 0xC1, 0xD7,
0xB9, 0xE9, 0x3D, 0xC6, 0xE8, 0x97, 0xF2, 0xAD, 0xBD, 0xF1, 0x27, 0x63, 0xCE, 0xF7, 0x39, 0xB9,
0x53, 0x8C, 0x95, 0x71, 0x66, 0x9B, 0x11, 0x27, 0x8A, 0x93, 0x34, 0xF8, 0xD4, 0x71, 0x63, 0xBD,
0x3E, 0xF7, 0x3F, 0x4E, 0xE0, 0x5C, 0x70, 0x1F, 0x2F, 0x76, 0x43, 0xDB, 0x00, 0x39, 0x39, 0x05,
0x5E, 0x0C, 0x02, 0x15, 0x10, 0xCC, 0x36, 0x33, 0x75, 0x9F, 0xD2, 0xFE, 0x36, 0xE5, 0x38, 0x77,
0xF2, 0xB8, 0x2B, 0xEB, 0xF6, 0x2E, 0x8F, 0xF9, 0xA4, 0xCD, 0xCA, 0xBF, 0xD2, 0x73, 0xBB, 0x8C,
0x7F, 0x7F, 0xEE, 0xB9, 0x00, 0xCF, 0xA8, 0xDB, 0x55, 0x7B, 0xB5, 0x57, 0xE3, 0x4B, 0x56, 0xF5,
0x5B, 0xBF, 0x6C, 0xDF, 0xCF, 0xBF, 0xC6, 0x0E, 0x9F, 0x79, 0x73, 0x3D, 0xA5, 0x95, 0xDF, 0x7A,
0xBD, 0xB6, 0x94, 0x7A, 0xF1, 0x6D, 0x9E, 0xA3, 0x7E, 0x45, 0xE3, 0x38, 0x5B, 0xBB, 0x2C, 0x94,
0x86, 0xEB, 0x23, 0xF8, 0x0E, 0x9C, 0x2F, 0x2F, 0xDF, 0x00, 0x1C, 0x81, 0x38, 0x1B, 0x99, 0xE3,
0x8D, 0x47, 0x05, 0x01, 0x66, 0x3E, 0xCB, 0x38, 0x32, 0x3D, 0xE2, 0x87, 0x95, 0xBC, 0x92, 0xCB,
0xF8, 0x14, 0x95, 0xB2, 0xDA, 0x1E, 0x89, 0x6F, 0xB5, 0x5B, 0x59, 0xB6, 0xF9, 0x96, 0x3B, 0xCA,
0x66, 0x5E, 0x63, 0x23, 0xCB, 0x78, 0x6A, 0x4B, 0x3F, 0xDB, 0x66, 0xBF, 0xDA, 0x6E, 0x79, 0x1D,
0x7B, 0xCC, 0xB1, 0xF6, 0x47, 0xDD, 0x68, 0xF7, 0x90, 0xE6, 0xFB, 0xF7, 0x0D, 0xEE, 0xEB, 0x63,
0x6D, 0xAD, 0x93, 0x81, 0xD2, 0x79, 0x5F, 0x0F, 0xFA, 0xA3, 0x4D, 0xE5, 0xC5, 0xBE, 0xF0, 0xBF,
0x97, 0x34, 0xE6, 0x7D, 0x7A, 0xA6, 0x4F, 0xD2, 0x06, 0x04, 0xC7, 0xE0, 0x19, 0x5C, 0x07, 0xBE,
0x9F, 0x5E, 0xFC, 0xCF, 0xFC, 0x31, 0xE6, 0x93, 0xD0, 0x8B, 0x5E, 0xF4, 0xA7, 0x12, 0x5F, 0x75,
0xC0, 0x31, 0x78, 0xFE, 0xAD, 0x2B, 0x77, 0xCB, 0x33, 0xCE, 0xB5, 0x6A, 0xFF, 0x0E, 0x7F, 0xB5,
0x5F, 0xED, 0xBF, 0xD2, 0xAE, 0xFC, 0xCE, 0xF5, 0xF6, 0xF6, 0x1F, 0xC6, 0x74, 0x9C, 0x6E, 0x73,
0x87, 0xE8, 0xBB, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
};

const unsigned char END_BUTTON[] = {
	// 这里需要添加结束按钮的图片数据
0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52,
0x00, 0x00, 0x00, 0xBF, 0x00, 0x00, 0x00, 0x24, 0x08, 0x06, 0x00, 0x00, 0x00, 0xD8, 0x4B, 0xEB,
0x86, 0x00, 0x00, 0x00, 0x01, 0x73, 0x52, 0x47, 0x42, 0x00, 0xAE, 0xCE, 0x1C, 0xE9, 0x00, 0x00,
0x00, 0x04, 0x67, 0x41, 0x4D, 0x41, 0x00, 0x00, 0xB1, 0x8F, 0x0B, 0xFC, 0x61, 0x05, 0x00, 0x00,
0x00, 0x09, 0x70, 0x48, 0x59, 0x73, 0x00, 0x00, 0x0E, 0xC3, 0x00, 0x00, 0x0E, 0xC3, 0x01, 0xC7,
0x6F, 0xA8, 0x64, 0x00, 0x00, 0x11, 0xB8, 0x49, 0x44, 0x41, 0x54, 0x78, 0x5E, 0xED, 0x9D, 0x8B,
0x79, 0x23, 0x37, 0xB2, 0x85, 0x27, 0x05, 0xA5, 0xA0, 0x14, 0x94, 0x02, 0x53, 0x50, 0x0A, 0x4C,
0x41, 0x29, 0x28, 0x05, 0xA5, 0xA0, 0x14, 0x94, 0x02, 0x53, 0x60, 0x0A, 0x93, 0x82, 0xEE, 0xF9,
0xEB, 0x01, 0x14, 0xD0, 0x00, 0xC9, 0x19, 0x7B, 0x7D, 0xD7, 0xFB, 0x4D, 0xAF, 0x31, 0xF5, 0x2E,
0x14, 0x80, 0x03, 0x34, 0xD8, 0xF6, 0xDA, 0x3F, 0xEE, 0x3C, 0x4F, 0xD1, 0x78, 0x1E, 0xA5, 0x3C,
0x8F, 0xFA, 0xDE, 0xA2, 0x7F, 0xF8, 0x3F, 0xFC, 0xDF, 0xC5, 0x3F, 0xFC, 0xE0, 0xFC, 0xAC, 0x76,
0x52, 0x7B, 0x55, 0x3B, 0xFF, 0x69, 0x7F, 0xDA, 0xBF, 0xB4, 0x81, 0x5F, 0x70, 0x0C, 0x9E, 0x1F,
0x7A, 0x4E, 0x2F, 0xCF, 0xCF, 0x6F, 0xD7, 0xD7, 0xB7, 0xEB, 0xCF, 0xD7, 0x8F, 0xEF, 0xEF, 0xF3,
0xC7, 0x37, 0xD4, 0x9A, 0xF8, 0xEF, 0xE0, 0x53, 0x6F, 0xF2, 0x03, 0xFA, 0xA6, 0x33, 0xFA, 0x39,
0xF8, 0x34, 0x7B, 0xF2, 0x99, 0xA3, 0xC8, 0x43, 0xBE, 0xA4, 0xD5, 0x67, 0xC3, 0x2F, 0xE3, 0x26,
0xDB, 0xC1, 0x0F, 0x39, 0xF4, 0xCD, 0x3E, 0xC9, 0x83, 0x2E, 0xF4, 0x19, 0x3B, 0xF3, 0xE6, 0x3B,
0xE9, 0x5B, 0x7D, 0x41, 0x77, 0xF1, 0xB5, 0x8F, 0x5D, 0x8E, 0xC1, 0x9E, 0x34, 0xFD, 0x8A, 0xFF,
0x10, 0x5B, 0x69, 0xE4, 0x59, 0xD5, 0xB2, 0xED, 0xBF, 0xE8, 0x0F, 0x79, 0x8B, 0x3C, 0xF3, 0xD5,
0xE7, 0xE0, 0x1F, 0x3A, 0xD3, 0x27, 0xBF, 0x88, 0x59, 0xF9, 0x1D, 0xEA, 0xA4, 0x49, 0x77, 0x11,
0x8E, 0x4F, 0xCF, 0xCF, 0xEF, 0xC2, 0xF5, 0x8B, 0xDA, 0xF6, 0x2D, 0x80, 0xE1, 0xF4, 0x71, 0x3A,
0x7F, 0x11, 0x74, 0x3D, 0x9D, 0xBF, 0x2F, 0x2F, 0xAF, 0xDF, 0x5F, 0x6A, 0xD0, 0x6C, 0x55, 0x5E,
0xF1, 0x33, 0xDD, 0xF1, 0x55, 0x57, 0xDB, 0xAC, 0xBF, 0x17, 0xBB, 0xE3, 0xAB, 0xBC, 0x8A, 0x9B,
0xE5, 0x7B, 0xBE, 0x8F, 0xEA, 0xAA, 0xBC, 0xB3, 0xCD, 0x74, 0xB6, 0x57, 0xFE, 0x11, 0x9F, 0xDA,
0x56, 0x71, 0x8F, 0xF0, 0x3B, 0xDB, 0xCE, 0x67, 0xD6, 0xCF, 0xBA, 0x1D, 0xBF, 0xB3, 0xDD, 0xF2,
0x59, 0xC9, 0xB5, 0xAD, 0x72, 0x24, 0x7F, 0x3D, 0xBD, 0x69, 0x23, 0xBC, 0x7F, 0x7F, 0x9E, 0x5E,
0xBF, 0xC0, 0xB7, 0x21, 0x7D, 0xB1, 0x09, 0x4E, 0x9F, 0xA7, 0xF3, 0xE5, 0xA7, 0x9C, 0xE7, 0x44,
0xF7, 0xE4, 0xD9, 0xB6, 0xD3, 0xEF, 0xE4, 0x81, 0x3E, 0x8F, 0xBA, 0x6C, 0xBF, 0x22, 0xCF, 0x36,
0xD3, 0x45, 0x5E, 0xE3, 0x8B, 0xFE, 0x9E, 0x9C, 0x71, 0xAB, 0xFC, 0x37, 0xE3, 0x0A, 0x7F, 0x4F,
0xAE, 0xB5, 0xCD, 0xB6, 0x2A, 0x0F, 0x31, 0x0B, 0xFE, 0x10, 0xB7, 0x19, 0xF3, 0x32, 0xF6, 0x81,
0xF9, 0x69, 0xFA, 0x85, 0x6F, 0x8D, 0x79, 0x84, 0x5F, 0xC9, 0x2B, 0xDD, 0x4E, 0x9E, 0x69, 0x6D,
0xAB, 0x98, 0xEB, 0x89, 0x0D, 0x70, 0xBE, 0x80, 0x73, 0x87, 0x7B, 0x7F, 0x9E, 0x78, 0x35, 0xB0,
0x43, 0xCC, 0x79, 0x5A, 0xF0, 0x46, 0x37, 0x7A, 0x26, 0xE3, 0xEB, 0xE5, 0x74, 0xD3, 0xC7, 0xE9,
0x69, 0xA3, 0x5F, 0xE8, 0x2C, 0x67, 0xC8, 0x35, 0xE7, 0x0D, 0x7D, 0xE3, 0xC3, 0xD6, 0xF8, 0xA0,
0xA9, 0x1F, 0xFC, 0xA0, 0x8B, 0x9C, 0x07, 0x1F, 0x9A, 0xF9, 0xC5, 0x38, 0x07, 0x5D, 0xF1, 0x5D,
0xC5, 0x87, 0xEE, 0xA6, 0xCF, 0x4C, 0xA7, 0x98, 0xC6, 0x47, 0x7F, 0x83, 0xAE, 0xD0, 0x95, 0xAE,
0xD9, 0x4A, 0xEC, 0xCA, 0xBF, 0xF1, 0x77, 0xFC, 0x76, 0x74, 0xE0, 0x87, 0x3A, 0xCB, 0xBA, 0xAF,
0xC6, 0x35, 0xE9, 0x8C, 0xB6, 0xF8, 0x7D, 0x6C, 0xFA, 0x75, 0xDD, 0x6D, 0x7C, 0x81, 0x6F, 0xAE,
0xF4, 0xE0, 0xDD, 0x61, 0xEF, 0xCF, 0xE9, 0xEB, 0x74, 0xBE, 0xF2, 0x8A, 0xA8, 0xCE, 0xC6, 0xB7,
0x22, 0x26, 0xBA, 0xD3, 0x57, 0xBA, 0x2A, 0x96, 0xF6, 0x5C, 0x07, 0x34, 0x81, 0xA9, 0xF0, 0x8F,
0xD1, 0xDB, 0x03, 0x5E, 0xE9, 0x57, 0x7E, 0x2B, 0xFE, 0x11, 0xFF, 0xAE, 0x1B, 0xC7, 0xE1, 0x63,
0xEF, 0xB5, 0xDD, 0xAA, 0x73, 0x8E, 0x19, 0x6D, 0x92, 0xCB, 0x5C, 0x8F, 0xFE, 0xEE, 0xDB, 0x75,
0xD1, 0xC7, 0xC1, 0x7F, 0x91, 0x73, 0x90, 0x2B, 0xED, 0x39, 0x9B, 0x6E, 0xDB, 0xFF, 0xDA, 0xE7,
0x40, 0xAB, 0x5F, 0xD2, 0x95, 0xFF, 0x83, 0x7E, 0xE8, 0x5C, 0x2E, 0xE3, 0x28, 0x7E, 0xA6, 0x5B,
0xE4, 0x02, 0xDF, 0x17, 0xE1, 0x1C, 0xBC, 0x3B, 0xEC, 0xFD, 0x39, 0xB7, 0x53, 0xBF, 0x39, 0xD7,
0xC5, 0x9A, 0x3B, 0x39, 0x4E, 0xDE, 0xD2, 0x37, 0x73, 0x4C, 0x03, 0xA8, 0x7C, 0xA3, 0xE1, 0x63,
0xF1, 0xCD, 0xBF, 0xD4, 0xD0, 0x36, 0xC9, 0xB1, 0xEF, 0xD4, 0x8F, 0x03, 0x76, 0xB9, 0xEB, 0x0B,
0xCD, 0x98, 0x22, 0xB7, 0x98, 0xA6, 0x1F, 0xF3, 0xA4, 0xDC, 0xED, 0x19, 0xD7, 0xEB, 0x41, 0xBF,
0xAA, 0x2F, 0xDB, 0x38, 0x0F, 0x9B, 0x71, 0x46, 0x9B, 0x73, 0x74, 0xDB, 0x22, 0xFF, 0x54, 0x5F,
0xCF, 0xB7, 0x1A, 0x4F, 0x91, 0xB3, 0x7F, 0xA3, 0x91, 0x37, 0x74, 0x19, 0xD7, 0xFB, 0x1B, 0xED,
0x63, 0xCD, 0xBD, 0xA6, 0xDB, 0xEB, 0x34, 0xD6, 0xD5, 0x62, 0x1A, 0xBF, 0x8A, 0x49, 0x3A, 0x8D,
0xA9, 0xE9, 0x6F, 0xF1, 0x59, 0x73, 0xCF, 0x7B, 0x15, 0xCE, 0x85, 0x77, 0xBE, 0x02, 0xD9, 0xE9,
0xCF, 0x1F, 0x06, 0xFE, 0x16, 0x34, 0x4C, 0x4A, 0x49, 0x5A, 0x92, 0xB8, 0x5E, 0x85, 0xE4, 0x04,
0x4D, 0x31, 0xE8, 0xBB, 0x6F, 0xDA, 0xCB, 0xA0, 0x9B, 0xDD, 0x73, 0xD4, 0x09, 0xC9, 0xB8, 0xF4,
0x6B, 0xFC, 0xAC, 0x53, 0x3D, 0x4E, 0x47, 0x1F, 0xAB, 0xF3, 0xFC, 0xFE, 0x7D, 0xFD, 0xFC, 0xFA,
0xFE, 0x79, 0xB9, 0x7E, 0x7F, 0xFF, 0xFC, 0xA9, 0xBF, 0xD4, 0xC4, 0xA3, 0xBB, 0xBC, 0xFA, 0x1B,
0xAE, 0xC5, 0x64, 0x2D, 0x2D, 0x8F, 0xE7, 0x6D, 0xFA, 0xD4, 0x21, 0xBF, 0x9E, 0x95, 0xE3, 0xF3,
0xFB, 0xF2, 0xF6, 0x31, 0xD8, 0xE1, 0xAF, 0xEF, 0xEF, 0xEA, 0xE3, 0xF2, 0x7D, 0xF9, 0xF8, 0x94,
0xAE, 0xCE, 0x49, 0x9F, 0xB7, 0x3E, 0x67, 0x29, 0x7B, 0x6C, 0xE5, 0xDD, 0x37, 0xFA, 0x4B, 0xFB,
0x29, 0x7C, 0x4A, 0xAC, 0xC7, 0x9D, 0xD4, 0x2F, 0xB5, 0xD4, 0x3E, 0xD2, 0xA6, 0x9A, 0x54, 0x0B,
0xF5, 0x7E, 0xA9, 0x6E, 0xCF, 0x17, 0x73, 0x66, 0xBE, 0x3D, 0xA6, 0xEB, 0x0A, 0xB5, 0xB1, 0x6A,
0xBE, 0x18, 0x4F, 0xE4, 0x1B, 0xE3, 0x45, 0x53, 0xDF, 0xEC, 0x4E, 0x47, 0x5B, 0xC4, 0xCC, 0xBE,
0x4D, 0x1F, 0x75, 0x48, 0xEF, 0x34, 0xE5, 0x59, 0x3F, 0x52, 0xCF, 0x23, 0x1F, 0x93, 0x3D, 0xD7,
0x30, 0xEF, 0x35, 0x26, 0x64, 0x70, 0x0E, 0xDE, 0x01, 0x7E, 0x3E, 0x0E, 0xFE, 0x28, 0xC6, 0x1D,
0x7B, 0xB2, 0x9A, 0xA0, 0xD9, 0x07, 0xDF, 0xD4, 0xBB, 0xCE, 0xE5, 0xB0, 0x4F, 0xB1, 0x59, 0x70,
0x93, 0x8B, 0xCD, 0x5A, 0xDA, 0x8D, 0x7A, 0x6B, 0xB5, 0x84, 0xAE, 0xD7, 0x56, 0x9B, 0xEB, 0xF4,
0x5A, 0x33, 0x00, 0x02, 0x76, 0x40, 0x5F, 0x5B, 0xD5, 0xFD, 0xFC, 0xBA, 0x08, 0x50, 0x02, 0xC4,
0x50, 0x4B, 0xE1, 0xA7, 0x3A, 0x5B, 0xDF, 0x02, 0xF8, 0xB7, 0x52, 0x18, 0xA0, 0xD2, 0x1E, 0xBE,
0x57, 0xE5, 0xCC, 0x4D, 0x56, 0xE7, 0xB2, 0x35, 0xFC, 0xA2, 0x3E, 0x40, 0x65, 0x80, 0x36, 0x9D,
0xD7, 0x6D, 0xB2, 0xB5, 0x73, 0x5B, 0x40, 0xEB, 0x43, 0xB2, 0xD5, 0x4C, 0x5E, 0x64, 0xD9, 0xB2,
0xD6, 0xCB, 0xF9, 0xCD, 0x87, 0x74, 0x5D, 0xF4, 0x29, 0x99, 0x9A, 0x78, 0x2E, 0x6F, 0x7E, 0xB8,
0x75, 0x5B, 0xE5, 0x23, 0xCE, 0x74, 0x9E, 0x1F, 0x5D, 0x8E, 0xF5, 0xE7, 0x55, 0x73, 0x85, 0x4F,
0xC6, 0x98, 0x3D, 0xF8, 0xA8, 0xA3, 0xF2, 0xBD, 0xF6, 0xD9, 0x7E, 0x4B, 0x97, 0xED, 0x11, 0xFF,
0x4D, 0xBC, 0xFA, 0x9D, 0xB1, 0xD8, 0x9B, 0x64, 0xD9, 0x0B, 0xF8, 0xA7, 0x93, 0x3F, 0x8B, 0xAE,
0x93, 0x38, 0x0F, 0x72, 0x67, 0x6B, 0xB1, 0x21, 0xCF, 0xBC, 0xC9, 0x0B, 0xFE, 0x96, 0xFF, 0xCD,
0xBC, 0x21, 0xA7, 0x5E, 0xD4, 0x80, 0x70, 0x2D, 0x00, 0x17, 0x7F, 0xF9, 0xF8, 0xF8, 0xFE, 0x64,
0xE1, 0xD5, 0x38, 0xC1, 0x00, 0x89, 0xD9, 0xF0, 0xC1, 0xAE, 0x98, 0xE3, 0x98, 0x76, 0xB2, 0xA8,
0xF2, 0x18, 0x98, 0x94, 0xEB, 0xB3, 0xC5, 0x89, 0xEA, 0x6D, 0xE2, 0x48, 0x81, 0x28, 0x2F, 0xA7,
0x71, 0xC6, 0x0D, 0x7E, 0x02, 0x7F, 0xD6, 0x20, 0x30, 0x03, 0xAC, 0xDC, 0x94, 0x4E, 0x23, 0x9E,
0xCD, 0x45, 0x9C, 0x6D, 0xD0, 0x57, 0xCB, 0x8D, 0x7F, 0xEF, 0xD3, 0xF3, 0x52, 0x07, 0xFE, 0xD7,
0xCB, 0x97, 0xDB, 0xD2, 0x2E, 0x8A, 0x4C, 0x1E, 0xEC, 0xD4, 0xED, 0xB6, 0xE2, 0x13, 0x3C, 0x1B,
0x91, 0xCD, 0xCC, 0x5B, 0x02, 0x7F, 0xE3, 0x69, 0xB9, 0x99, 0x55, 0x2F, 0xFD, 0x34, 0xBD, 0x9A,
0xF9, 0xE1, 0x1F, 0x6F, 0x1D, 0xEF, 0x33, 0x72, 0x46, 0xFF, 0xDE, 0x47, 0xB4, 0xC1, 0x96, 0x7A,
0xFA, 0x5F, 0xF1, 0x6A, 0x77, 0xFD, 0xD1, 0x87, 0x7D, 0xC7, 0x1F, 0x72, 0x9C, 0x36, 0xE0, 0x8F,
0x1F, 0xBB, 0xB3, 0x73, 0xD3, 0x2D, 0x12, 0x2D, 0x6D, 0x4D, 0x2E, 0xBA, 0x56, 0xD8, 0xA3, 0x72,
0xF0, 0x73, 0xDE, 0xD9, 0xB7, 0xDA, 0x39, 0x51, 0x03, 0xF8, 0x2C, 0xD8, 0x97, 0x40, 0xFD, 0xFE,
0xFC, 0x62, 0x94, 0x05, 0xB2, 0x45, 0x4A, 0x1D, 0x00, 0x0E, 0x3F, 0x62, 0x1C, 0x60, 0x53, 0xBE,
0x5D, 0x8D, 0x11, 0x6B, 0xE0, 0x34, 0xBD, 0xDB, 0x00, 0x1F, 0xC0, 0x45, 0x6F, 0x79, 0x7F, 0xEA,
0x24, 0xE6, 0x7A, 0x35, 0xD5, 0x0B, 0x20, 0x3F, 0x54, 0xC3, 0xE5, 0x4B, 0x57, 0x32, 0x81, 0xCA,
0x4E, 0x66, 0xEA, 0x05, 0x9C, 0x34, 0x6D, 0xD6, 0xCC, 0x0F, 0xC8, 0xC4, 0xF2, 0x8D, 0x5A, 0xE0,
0xD7, 0x5E, 0xD5, 0x1B, 0xA3, 0x81, 0x5F, 0x14, 0x9E, 0x7E, 0xE8, 0xF7, 0x53, 0x9B, 0xEA, 0x53,
0x7A, 0x4E, 0x78, 0x7B, 0xF3, 0xE5, 0xC6, 0x52, 0x1F, 0xE4, 0x48, 0x1D, 0x39, 0x12, 0xCC, 0x56,
0x9B, 0xBD, 0x55, 0xF8, 0xCB, 0xFE, 0xF0, 0xC7, 0x02, 0x92, 0xBA, 0xD2, 0x36, 0xA6, 0x33, 0xEE,
0x16, 0xB2, 0xCD, 0x43, 0xD6, 0x74, 0x98, 0xC3, 0x95, 0x6E, 0xE7, 0xFB, 0x77, 0xEB, 0xAB, 0xAD,
0xDB, 0xC1, 0x79, 0x80, 0xBF, 0x3D, 0x67, 0xBE, 0x83, 0xB6, 0xC0, 0x7B, 0xB4, 0x76, 0xF8, 0x4B,
0x54, 0xAD, 0xE5, 0xDA, 0xE9, 0x27, 0x7A, 0x2B, 0x67, 0xE6, 0x91, 0x2F, 0x8B, 0x9B, 0x8B, 0xF5,
0xC1, 0xD5, 0xA1, 0xBC, 0x05, 0x0C, 0x00, 0x6A, 0xC9, 0x03, 0x4A, 0x7C, 0xD2, 0x1F, 0x00, 0xDE,
0xEB, 0x0F, 0x20, 0xF2, 0x18, 0x08, 0xC8, 0x13, 0x94, 0xC5, 0xE7, 0xED, 0x62, 0xD8, 0x51, 0xEE,
0xF7, 0xA7, 0xE7, 0x00, 0x30, 0xE6, 0x78, 0x03, 0x90, 0x3B, 0x72, 0x01, 0x58, 0xDB, 0x00, 0x4F,
0x2F, 0xF2, 0xD5, 0x26, 0x20, 0xAF, 0x7C, 0x89, 0xB9, 0x6A, 0x43, 0xB8, 0xAC, 0xB8, 0x38, 0x85,
0xE1, 0x0D, 0xFC, 0xF2, 0xE9, 0xE0, 0xF7, 0x7C, 0xD9, 0x2F, 0x71, 0xB9, 0x41, 0x0C, 0xE4, 0xAA,
0xE3, 0xCA, 0x98, 0x6D, 0x03, 0xF8, 0xB8, 0xAB, 0x4C, 0x23, 0x96, 0xBA, 0x2C, 0xDF, 0x9B, 0xE6,
0x43, 0x39, 0x3F, 0xC4, 0xBF, 0x17, 0xCA, 0x1B, 0xD3, 0x62, 0x15, 0xF7, 0xF1, 0xF2, 0x62, 0xFA,
0xF4, 0x83, 0xD2, 0x27, 0x9B, 0xAE, 0xCD, 0x5D, 0x52, 0xD9, 0x0E, 0x73, 0xB8, 0xB3, 0x9B, 0xFE,
0x11, 0x9F, 0x7B, 0xFA, 0xC2, 0xDF, 0xA0, 0xE0, 0x1C, 0xBC, 0xAB, 0xCD, 0x27, 0x3F, 0x93, 0x21,
0x27, 0xA3, 0x1E, 0x90, 0x94, 0x40, 0x4E, 0x96, 0x47, 0x68, 0x8D, 0x1B, 0xA9, 0x7C, 0xA6, 0xFC,
0x1E, 0x27, 0x5E, 0xFA, 0xA4, 0x36, 0xB0, 0xE6, 0xB7, 0xA6, 0xDD, 0x2E, 0x5E, 0x40, 0x4F, 0xB0,
0xC1, 0x7F, 0x22, 0x4B, 0xC1, 0xEB, 0x9C, 0x45, 0x02, 0x64, 0xB4, 0x0F, 0xF5, 0x07, 0x50, 0xD2,
0x8F, 0x93, 0x36, 0x41, 0x6C, 0xA7, 0xF4, 0xA6, 0x1F, 0xFA, 0xE0, 0xFE, 0x6B, 0xAF, 0xFE, 0xD8,
0x64, 0x50, 0x03, 0xA8, 0x40, 0x94, 0x9B, 0xEC, 0x8B, 0xD3, 0x54, 0x39, 0x01, 0x0D, 0xFD, 0xA0,
0x6B, 0xBE, 0x8A, 0xB7, 0x9A, 0xD9, 0x98, 0xEA, 0x8B, 0x9C, 0x96, 0x57, 0x00, 0xB7, 0x7A, 0x12,
0xC8, 0x96, 0xDF, 0x4F, 0x54, 0xF2, 0x73, 0xCE, 0xE6, 0x46, 0x05, 0xB4, 0x36, 0x5E, 0x1A, 0xD7,
0xA7, 0xC8, 0xFF, 0x21, 0x1E, 0xDF, 0xEC, 0xCB, 0xC6, 0x6A, 0x63, 0x7E, 0x6E, 0x9B, 0xEB, 0x13,
0x80, 0xE7, 0x3C, 0xA8, 0x65, 0xFF, 0xCC, 0xB9, 0x51, 0xBD, 0x01, 0xAC, 0xCF, 0xD2, 0x72, 0x0C,
0x6C, 0x9C, 0xDC, 0x8C, 0x9D, 0x7E, 0x28, 0x8F, 0xC7, 0xB6, 0x79, 0x5A, 0xD1, 0xE0, 0x57, 0xB4,
0xCF, 0x35, 0x7E, 0xE2, 0xAD, 0x1E, 0xA7, 0x29, 0xDF, 0xD2, 0x67, 0xDC, 0xAD, 0x1A, 0xE6, 0x7E,
0xF9, 0xDC, 0x59, 0xC1, 0xCF, 0xA3, 0x93, 0xBF, 0xBC, 0xA2, 0x0F, 0xB4, 0x24, 0x69, 0xFC, 0xCE,
0x67, 0xD6, 0x3F, 0x4A, 0xD5, 0x4A, 0x7C, 0x9D, 0x90, 0xA1, 0xFF, 0x42, 0x6D, 0xF2, 0x64, 0xB7,
0x53, 0x39, 0xC0, 0xF1, 0x2E, 0x99, 0xBB, 0x3C, 0xC0, 0x67, 0xA1, 0x3F, 0x75, 0xC5, 0x20, 0x17,
0x8D, 0xEB, 0x06, 0x0B, 0x8F, 0x0D, 0xC0, 0x22, 0xDB, 0x49, 0xC8, 0x02, 0x6B, 0x51, 0xAD, 0x4F,
0x5A, 0xA9, 0x2B, 0x27, 0xD6, 0x4E, 0x43, 0xC5, 0xE2, 0x4F, 0x5F, 0x5F, 0x01, 0x80, 0x04, 0x60,
0x52, 0x80, 0x66, 0x7F, 0x88, 0x07, 0x88, 0xAE, 0x8F, 0xD3, 0x59, 0x39, 0xEC, 0x0D, 0xA5, 0x07,
0xD9, 0x7E, 0xE8, 0x46, 0xED, 0x5F, 0x7A, 0x43, 0x40, 0x91, 0x01, 0x7C, 0x82, 0x0C, 0x9D, 0x81,
0x9F, 0x18, 0xC5, 0xE6, 0x58, 0xF2, 0xBA, 0xC4, 0xFF, 0x8C, 0x1A, 0xE9, 0x6F, 0xBD, 0xBC, 0x46,
0xB5, 0xCD, 0xCE, 0x49, 0x1F, 0x3A, 0x80, 0xEE, 0x63, 0x4D, 0x90, 0xA8, 0xD9, 0x8F, 0x5B, 0xCF,
0x95, 0xCD, 0x6A, 0xE7, 0x31, 0xD9, 0x98, 0xA6, 0xAF, 0x1B, 0x71, 0x07, 0xD8, 0xE3, 0x3C, 0xBA,
0xFC, 0x88, 0x7F, 0xD2, 0x1A, 0xD7, 0xD6, 0xBC, 0x60, 0x60, 0x4F, 0xD5, 0x0E, 0x79, 0x38, 0xF9,
0xC7, 0x6B, 0x8F, 0x9D, 0xFC, 0x28, 0xE7, 0xC1, 0xE4, 0xC4, 0xAC, 0x69, 0x9F, 0xB8, 0xDB, 0xFA,
0x2A, 0xD7, 0xBC, 0xF7, 0xE3, 0x29, 0x76, 0x3C, 0x1D, 0x26, 0x99, 0x18, 0xB5, 0x04, 0x70, 0xDE,
0xEB, 0x59, 0xA3, 0x7C, 0x3D, 0x63, 0xB7, 0x1F, 0xB5, 0x6A, 0xF0, 0x34, 0xDE, 0x00, 0x2C, 0x22,
0xBA, 0x3C, 0x31, 0xEB, 0x8F, 0x49, 0xA7, 0xB5, 0xAF, 0xD0, 0x9D, 0xE3, 0x7E, 0x4C, 0x5F, 0x8A,
0x03, 0x48, 0xC6, 0x03, 0x6C, 0xD9, 0xFC, 0x87, 0xB5, 0xFA, 0x01, 0x60, 0x6A, 0x00, 0x91, 0xDF,
0x18, 0x80, 0xD8, 0xAE, 0x08, 0xE4, 0x90, 0x3E, 0x4F, 0x77, 0xA8, 0xF5, 0xAF, 0xC7, 0xAE, 0x18,
0x52, 0x5E, 0xB4, 0x31, 0xAD, 0xB6, 0x19, 0xFC, 0xA2, 0x5C, 0x3F, 0x72, 0x33, 0xB3, 0xF1, 0x32,
0xDE, 0x36, 0xA4, 0x62, 0x01, 0x38, 0x9B, 0xDB, 0x65, 0x42, 0x40, 0xAC, 0x03, 0x36, 0x14, 0x26,
0x73, 0x58, 0xE4, 0x18, 0x73, 0x0E, 0xA9, 0x8B, 0x87, 0x7C, 0x76, 0xF5, 0x61, 0xDC, 0xE8, 0xAC,
0x5F, 0xBD, 0x4D, 0x42, 0x67, 0x87, 0x8B, 0x9E, 0xDC, 0x88, 0xC3, 0xBA, 0x21, 0x13, 0xC7, 0xBC,
0x2D, 0xF4, 0xDE, 0x97, 0xCF, 0x69, 0xEB, 0x37, 0xE7, 0x1B, 0xF9, 0x90, 0xC7, 0xE5, 0x9E, 0xB3,
0xF2, 0xB3, 0xFF, 0x8E, 0x46, 0x1E, 0xC9, 0xC4, 0xCC, 0x27, 0xBF, 0x83, 0xFF, 0x25, 0x5F, 0xFB,
0x8B, 0x04, 0x74, 0x90, 0x1D, 0xFD, 0x16, 0xA5, 0xD0, 0xA9, 0xE0, 0x7B, 0xB4, 0xC6, 0x48, 0x6E,
0x83, 0x4E, 0x1A, 0x76, 0xFC, 0x6C, 0x91, 0xD5, 0x58, 0x40, 0x36, 0x00, 0x8B, 0x0F, 0x08, 0x3C,
0x56, 0x03, 0xD6, 0x82, 0xE6, 0xC9, 0x4E, 0xCB, 0x13, 0xDF, 0x36, 0x4B, 0x2C, 0x30, 0x39, 0x12,
0x58, 0xC7, 0x09, 0x8E, 0x3C, 0x71, 0x8A, 0xA6, 0x3F, 0xA7, 0x2F, 0xB9, 0xB8, 0x66, 0x71, 0xAD,
0x30, 0x60, 0x93, 0x37, 0x40, 0x88, 0x3F, 0x0D, 0xF0, 0xD6, 0xBE, 0x79, 0x83, 0x00, 0x54, 0xEE,
0xCB, 0xED, 0xE4, 0x97, 0x0C, 0x4E, 0x4D, 0xD6, 0x03, 0x08, 0xE1, 0xE9, 0x67, 0x38, 0xF9, 0xE3,
0xAA, 0x91, 0x79, 0xDA, 0x15, 0x4F, 0xB5, 0xE4, 0x9B, 0xCE, 0x36, 0xBA, 0x6A, 0xA1, 0x9E, 0xDC,
0x88, 0x59, 0x9F, 0xF1, 0xFC, 0x38, 0x56, 0x8E, 0x9C, 0x5B, 0xE3, 0xE3, 0xE4, 0x67, 0xD3, 0x19,
0xF0, 0x69, 0xF2, 0xB5, 0xDC, 0xDC, 0xF9, 0xE9, 0x53, 0x3A, 0x1A, 0x7E, 0x8C, 0xF3, 0x93, 0xFE,
0x6C, 0x7E, 0x62, 0xDD, 0x88, 0x19, 0xE6, 0x2D, 0x72, 0xAF, 0x70, 0xF4, 0x0B, 0x7A, 0xE7, 0xA3,
0xA6, 0xC2, 0x77, 0xFF, 0xD0, 0x1F, 0xE8, 0x98, 0x8F, 0x18, 0x70, 0x5E, 0xC1, 0xCF, 0xA3, 0x93,
0xBF, 0x4E, 0x48, 0xA7, 0x1E, 0x5C, 0xF8, 0xBF, 0x89, 0xDE, 0xE5, 0xD5, 0x67, 0x8B, 0xC9, 0xC1,
0xA4, 0x0C, 0x0D, 0x3B, 0x20, 0x60, 0x31, 0x6C, 0xE1, 0xCE, 0x5A, 0x70, 0x2D, 0xA2, 0xDD, 0xF5,
0x01, 0x65, 0xF8, 0xDB, 0x9B, 0x81, 0xC5, 0x0A, 0x19, 0x80, 0xD8, 0xB5, 0x43, 0x8B, 0x4B, 0x4B,
0x40, 0xFB, 0x86, 0xC9, 0x89, 0xEF, 0x7D, 0x58, 0x8C, 0x26, 0xB0, 0x01, 0x1F, 0x94, 0x42, 0x01,
0xAB, 0x40, 0x8B, 0x9E, 0x37, 0x07, 0xA7, 0x3E, 0x4A, 0x4E, 0x6F, 0x3B, 0x81, 0x71, 0xD0, 0x5F,
0xD4, 0xD4, 0xFB, 0xF6, 0x86, 0x9D, 0xC6, 0x5B, 0x03, 0x9F, 0x04, 0x7A, 0xA5, 0xF6, 0x56, 0x10,
0xDF, 0x4E, 0x7E, 0x1B, 0x43, 0x1F, 0x97, 0x2D, 0xA6, 0x74, 0xD8, 0x6C, 0x03, 0xC6, 0x3C, 0x01,
0x50, 0x1F, 0x5B, 0x6E, 0x82, 0x0E, 0x7C, 0xA3, 0xE1, 0x47, 0x6B, 0x73, 0x2B, 0xBD, 0x3F, 0x5E,
0x73, 0x1B, 0x6B, 0xD0, 0xD4, 0x27, 0xE5, 0xFA, 0xC6, 0x86, 0x18, 0xD6, 0x24, 0x29, 0xFD, 0xA7,
0x9C, 0xF9, 0xFF, 0x82, 0xDE, 0x79, 0xDF, 0x54, 0x95, 0xAF, 0xFE, 0x6D, 0xD3, 0x1D, 0xF8, 0x88,
0x09, 0xBA, 0xBC, 0xF6, 0x5C, 0x5E, 0x1C, 0xFC, 0x6D, 0x62, 0x66, 0x7A, 0xCB, 0xF6, 0x9F, 0xA2,
0x3B, 0xBE, 0xEA, 0xD4, 0x0C, 0xDC, 0x5A, 0x20, 0x7B, 0x65, 0x07, 0xA8, 0xD2, 0x66, 0x9F, 0x40,
0x63, 0x01, 0xF3, 0xAE, 0x4B, 0x4B, 0xBF, 0x04, 0x58, 0xBE, 0x2D, 0x76, 0x7D, 0xB0, 0x59, 0xF0,
0xE3, 0x14, 0x84, 0x3A, 0x68, 0xFD, 0xC4, 0xCE, 0x1F, 0xBC, 0xC3, 0x09, 0x1D, 0x27, 0x72, 0xCB,
0x3B, 0xE5, 0x4C, 0x70, 0x5F, 0xAF, 0x7E, 0x77, 0x47, 0xE6, 0xB1, 0xBB, 0xBF, 0x1E, 0x6C, 0x9C,
0xC2, 0xE4, 0x18, 0xC0, 0x9F, 0xB9, 0xF8, 0xC1, 0x1B, 0xB5, 0xD0, 0x3F, 0x7D, 0x5A, 0x7F, 0xB6,
0x09, 0xB5, 0xA1, 0x0D, 0xA1, 0xA8, 0xF8, 0x03, 0xE2, 0x35, 0xA2, 0xB4, 0x0D, 0x30, 0xD5, 0xF3,
0x71, 0xD2, 0x5C, 0xA1, 0xB7, 0xC6, 0x97, 0x32, 0x9F, 0x53, 0x8B, 0x31, 0xA2, 0x38, 0x36, 0x71,
0xF1, 0xC9, 0xD8, 0x21, 0x57, 0x9D, 0xB7, 0xFF, 0x0F, 0xFE, 0x0E, 0xE5, 0x90, 0x07, 0xEF, 0x81,
0x7B, 0x7B, 0x74, 0xED, 0xE9, 0xC0, 0xF8, 0x37, 0x35, 0x4E, 0x9F, 0x19, 0xC0, 0x79, 0xD7, 0xA7,
0x71, 0x32, 0x9B, 0x4D, 0x8D, 0x6B, 0xCE, 0x1C, 0x4B, 0x8C, 0xFD, 0xCD, 0x2E, 0xE5, 0xA8, 0x6F,
0x8B, 0xDA, 0xB8, 0x46, 0x10, 0x6F, 0x00, 0xD0, 0xC2, 0xF3, 0x00, 0x4C, 0xBE, 0xA6, 0xB0, 0x89,
0x0C, 0x78, 0xB2, 0xF1, 0xD5, 0xC5, 0xF2, 0x09, 0x2C, 0x97, 0x4F, 0xBF, 0xC6, 0xE4, 0x3D, 0x7C,
0xCC, 0xD9, 0x7F, 0x68, 0x27, 0x80, 0x73, 0x53, 0x01, 0x2C, 0x4E, 0x55, 0xE2, 0x6C, 0x43, 0x90,
0xD7, 0xC0, 0xEF, 0x9B, 0x2A, 0xC7, 0x66, 0x75, 0xC8, 0xC6, 0x63, 0x79, 0xB4, 0x89, 0x78, 0xE3,
0x10, 0x97, 0x36, 0xE2, 0xF3, 0xAE, 0x6E, 0xF3, 0x24, 0x99, 0x10, 0x07, 0xFF, 0x58, 0x13, 0x76,
0x9B, 0x3B, 0xB5, 0xFC, 0xDD, 0x41, 0xDE, 0x4A, 0xA9, 0x91, 0x31, 0xFB, 0x1C, 0xAF, 0xE7, 0xEA,
0xBF, 0xBD, 0x71, 0xC8, 0x83, 0x77, 0xB5, 0x7E, 0xE7, 0x6F, 0x27, 0xFF, 0xBF, 0xAE, 0x69, 0x11,
0x04, 0xBA, 0x5C, 0x24, 0xFE, 0x26, 0x4F, 0x2E, 0x2C, 0x0B, 0xDA, 0xBE, 0x8A, 0xB0, 0x78, 0xE2,
0xF3, 0xE4, 0x64, 0xF1, 0x12, 0x24, 0xD8, 0xEC, 0x1E, 0x1C, 0xF7, 0xE9, 0xB1, 0xC5, 0x66, 0x92,
0x1D, 0x60, 0xD9, 0xA9, 0x48, 0x3F, 0x02, 0x52, 0x82, 0xC0, 0x41, 0xEA, 0x9F, 0x13, 0x79, 0x23,
0xE0, 0xD7, 0xC0, 0xAC, 0xB8, 0x04, 0x6C, 0x36, 0xDB, 0x4C, 0x02, 0x2D, 0x6F, 0x93, 0x37, 0xC0,
0x14, 0xF5, 0xF3, 0x16, 0xE0, 0x74, 0xE5, 0xF4, 0x67, 0x53, 0x91, 0x87, 0x4D, 0x62, 0x6F, 0x11,
0xB3, 0xF7, 0xB7, 0x88, 0x81, 0x4F, 0xF5, 0x13, 0x4B, 0x8E, 0xF7, 0x1F, 0xFD, 0x33, 0x26, 0xE3,
0xD2, 0xA8, 0x6C, 0x43, 0x9B, 0x4E, 0xBE, 0xD0, 0xDC, 0x4C, 0xD8, 0xE7, 0x9A, 0xAC, 0x69, 0x93,
0xE4, 0x1B, 0xCD, 0xFA, 0x65, 0x7E, 0x94, 0x87, 0xB1, 0xE4, 0x06, 0xA7, 0x66, 0xAE, 0x1D, 0xCB,
0xF8, 0x6D, 0xF3, 0x9A, 0xFF, 0x5A, 0xBB, 0x97, 0xE3, 0xB1, 0x3E, 0x0A, 0xF8, 0xED, 0x09, 0xF0,
0xD7, 0x01, 0xDD, 0xEF, 0x28, 0x7D, 0x3B, 0x5D, 0xC7, 0xAC, 0x27, 0x69, 0x97, 0xDF, 0xF5, 0xAB,
0x3A, 0x32, 0xFF, 0xDC, 0x1F, 0x32, 0x0B, 0xEB, 0x7F, 0x73, 0xC7, 0x4F, 0x28, 0x03, 0x32, 0x36,
0x16, 0x3C, 0xDE, 0x0A, 0x34, 0x4E, 0xFE, 0x8C, 0xE7, 0x3B, 0x79, 0xEA, 0xED, 0x44, 0x0D, 0x90,
0x60, 0xAB, 0xAD, 0xF5, 0x27, 0xBB, 0x01, 0xD2, 0x00, 0xE1, 0xD7, 0x92, 0x8C, 0x41, 0x07, 0x50,
0xBD, 0x96, 0x02, 0x66, 0xE5, 0x45, 0x1E, 0x73, 0xF5, 0x93, 0x35, 0x41, 0x6E, 0x57, 0x2A, 0x3D,
0xBC, 0x4D, 0xF2, 0x94, 0xB5, 0x7C, 0xD1, 0x27, 0x27, 0x37, 0x60, 0xE6, 0x2D, 0x81, 0xCE, 0x73,
0xB9, 0xDE, 0x36, 0xA5, 0xFA, 0x07, 0xD8, 0x34, 0xFA, 0x34, 0xD0, 0x2A, 0x87, 0xD5, 0x88, 0x8F,
0xD6, 0xD6, 0x80, 0x2D, 0x3B, 0xF9, 0x0D, 0xFC, 0x91, 0xC7, 0x1B, 0x63, 0x78, 0x33, 0x90, 0x13,
0xC7, 0x26, 0xE3, 0x2B, 0xD5, 0xA7, 0xFD, 0x08, 0xF6, 0x37, 0x0E, 0x72, 0x7B, 0x5B, 0x89, 0xE6,
0x8F, 0xFA, 0x9E, 0xA3, 0xCF, 0x55, 0xE5, 0x77, 0xD4, 0xF9, 0x1C, 0x4B, 0xEA, 0x2A, 0xAE, 0x46,
0xDB, 0xAC, 0xDF, 0xB7, 0xD9, 0xDE, 0x73, 0xD2, 0xC0, 0x39, 0x78, 0x0F, 0xDC, 0xDB, 0x63, 0xE0,
0xAF, 0x41, 0x35, 0xA0, 0x76, 0x58, 0xF5, 0xAB, 0x76, 0x1C, 0x10, 0x6D, 0x2C, 0xA0, 0xF3, 0x3B,
0xFD, 0x6D, 0xDD, 0xDC, 0xE8, 0x93, 0xAB, 0x81, 0x01, 0x47, 0x1B, 0xC0, 0x16, 0x87, 0x05, 0x46,
0xCF, 0x02, 0xF1, 0x63, 0x50, 0x20, 0x31, 0x20, 0x49, 0x67, 0xFF, 0x08, 0x00, 0x8B, 0x68, 0x0B,
0xA9, 0x6B, 0x85, 0x80, 0x91, 0xFD, 0xCC, 0x34, 0x27, 0xD3, 0xC6, 0x45, 0x2E, 0x03, 0x84, 0x00,
0x04, 0xD8, 0x38, 0x99, 0xE3, 0x54, 0xE4, 0xDA, 0x01, 0x78, 0xD8, 0x84, 0x06, 0x22, 0xC0, 0xAA,
0x3A, 0x52, 0x07, 0x10, 0x6D, 0xBC, 0x9A, 0x67, 0xAB, 0x53, 0x2D, 0x3F, 0x83, 0x22, 0xB6, 0x8D,
0xA2, 0x3A, 0xDD, 0xAE, 0x37, 0xC9, 0x59, 0x6F, 0x0D, 0x6A, 0x56, 0x7D, 0x06, 0x42, 0xF5, 0x91,
0x80, 0xF3, 0xDF, 0x20, 0xA4, 0xD1, 0x1F, 0xCE, 0xD8, 0x06, 0x22, 0x8F, 0xFD, 0xF0, 0xA6, 0x7F,
0xCB, 0xE3, 0xFA, 0xCC, 0x29, 0xAD, 0x83, 0x3F, 0xC6, 0xC5, 0xA6, 0xE0, 0x8D, 0x98, 0x76, 0xFA,
0xB0, 0xAB, 0x12, 0xFD, 0xC4, 0x26, 0x32, 0x9D, 0x64, 0xF4, 0xB9, 0x51, 0x33, 0x37, 0xF5, 0xCF,
0x9B, 0xC0, 0xDB, 0xA3, 0xBA, 0xDF, 0x6B, 0xAD, 0x7E, 0xE3, 0xAB, 0x7E, 0xCD, 0xA7, 0xCC, 0x3F,
0x74, 0x07, 0xDE, 0xD5, 0xCA, 0xB5, 0x47, 0xCA, 0x74, 0xCE, 0xC4, 0xC7, 0x0E, 0x52, 0xBF, 0xF6,
0x3B, 0x76, 0x76, 0xD4, 0x1F, 0x73, 0xAE, 0xF4, 0x73, 0x1C, 0xA0, 0x0D, 0x5B, 0x4C, 0xB4, 0xCB,
0xA1, 0xD7, 0x75, 0xC5, 0xF4, 0x0D, 0x38, 0x01, 0x6C, 0x01, 0x0F, 0x80, 0xDA, 0x57, 0x20, 0xAE,
0x24, 0xEF, 0x02, 0x9A, 0x00, 0x69, 0x1B, 0x04, 0x3F, 0xD1, 0xDC, 0x14, 0xBD, 0x8F, 0xAC, 0xA3,
0xF4, 0x59, 0xA8, 0x9D, 0xFC, 0x2C, 0xBA, 0xC0, 0x9C, 0xD7, 0x12, 0x4E, 0x4A, 0xDB, 0x04, 0x46,
0x05, 0xFE, 0x26, 0x0B, 0x54, 0x41, 0xF3, 0x4D, 0xE1, 0xE0, 0xE1, 0xAD, 0xE0, 0xB5, 0x01, 0x38,
0xFE, 0x60, 0x83, 0x62, 0xA7, 0x16, 0xBB, 0x36, 0xC9, 0x0F, 0xD0, 0xDB, 0xA7, 0xDB, 0x18, 0x13,
0xC0, 0xC6, 0xC7, 0xEA, 0x30, 0xBD, 0x72, 0xCB, 0x0F, 0x7F, 0x80, 0x0A, 0x38, 0x6D, 0x2C, 0x56,
0xA3, 0xDE, 0x4E, 0xD8, 0xC4, 0xDB, 0x8F, 0x53, 0x6D, 0x5A, 0xDF, 0x30, 0x09, 0xFE, 0x18, 0x13,
0x9B, 0x91, 0x39, 0x61, 0x4C, 0x02, 0xB2, 0x1D, 0x10, 0xD6, 0x47, 0xBE, 0xE5, 0xBC, 0x56, 0xE4,
0xAC, 0x8F, 0xFC, 0xB6, 0xE1, 0x15, 0x63, 0x6F, 0x96, 0xCC, 0x15, 0x73, 0x77, 0x9C, 0xB7, 0x95,
0xBE, 0xCE, 0xEF, 0x6C, 0xAF, 0xF2, 0xDE, 0xAF, 0xFA, 0xAC, 0xFC, 0x8F, 0x6B, 0xA8, 0xC3, 0xA6,
0x9F, 0xFC, 0xED, 0x39, 0xE7, 0xB7, 0x50, 0x06, 0xE8, 0x81, 0xBD, 0x93, 0x6D, 0x71, 0xED, 0x9B,
0x73, 0xD0, 0x88, 0xC5, 0xBE, 0x8E, 0x9D, 0xF4, 0x80, 0x36, 0xF5, 0xE5, 0xFB, 0xF5, 0xEC, 0x3F,
0xC4, 0x4C, 0xB6, 0xD4, 0xD3, 0x6C, 0xE1, 0x34, 0x0E, 0x16, 0x86, 0xC5, 0xB4, 0x0D, 0x50, 0x5A,
0xEA, 0xA0, 0x06, 0xA4, 0x00, 0xCB, 0x98, 0xD7, 0x69, 0xE5, 0x5B, 0x1D, 0xF2, 0xB5, 0xEF, 0xE1,
0x7A, 0x38, 0xF9, 0x89, 0xCD, 0x66, 0xF7, 0xEE, 0xC6, 0x43, 0x9F, 0x83, 0xF6, 0x66, 0x75, 0xAA,
0x3E, 0x40, 0x06, 0x60, 0xD1, 0x55, 0xDE, 0xC7, 0xE1, 0xFD, 0xA4, 0x0C, 0xB5, 0x37, 0x99, 0x80,
0xDB, 0xDE, 0x04, 0xC5, 0xA7, 0x36, 0xB3, 0xA9, 0xE5, 0xD7, 0x9A, 0x96, 0x37, 0xEC, 0x6C, 0x36,
0x36, 0xA6, 0x6D, 0xB4, 0x1C, 0x33, 0x76, 0x1D, 0x00, 0xB4, 0xFE, 0x63, 0x36, 0xC6, 0xCA, 0x55,
0x48, 0xFE, 0xF6, 0xE9, 0x38, 0xF5, 0x51, 0x13, 0xBE, 0xAD, 0xCF, 0xD0, 0xCF, 0xF3, 0x35, 0xAF,
0xD1, 0xB0, 0xDE, 0x55, 0x6F, 0x54, 0x72, 0xE4, 0x3A, 0xE6, 0xD9, 0xC5, 0x1F, 0xED, 0xDB, 0xD8,
0xD0, 0xF3, 0x29, 0x1B, 0xBC, 0xAB, 0xF5, 0x93, 0xDF, 0xBF, 0x6F, 0x7B, 0x80, 0xB7, 0x0C, 0x90,
0xCE, 0x92, 0x16, 0x2A, 0xFD, 0x4C, 0x07, 0x7F, 0xF4, 0x56, 0x68, 0x2F, 0x98, 0x49, 0xCA, 0x02,
0xBA, 0xEF, 0x9E, 0x1F, 0xFA, 0x1B, 0xFA, 0x0D, 0xBF, 0xCC, 0x9B, 0x31, 0xD1, 0x1F, 0xFD, 0x00,
0x3A, 0x4E, 0x74, 0x4E, 0x5B, 0x3F, 0xD5, 0x38, 0xC0, 0x04, 0x7C, 0x40, 0xC4, 0x09, 0x1C, 0xA7,
0x7D, 0x2E, 0x9E, 0xC7, 0x47, 0xFE, 0x89, 0xF6, 0xBA, 0xBB, 0x9C, 0xD7, 0x82, 0xD6, 0xA2, 0xDF,
0xDB, 0x34, 0x72, 0x48, 0xEE, 0xA0, 0x49, 0x20, 0x39, 0x5F, 0xEB, 0x30, 0x39, 0x62, 0x6A, 0xAD,
0x96, 0x23, 0xEB, 0x99, 0x74, 0x3E, 0x47, 0xAE, 0xAB, 0x79, 0x5B, 0xDD, 0x55, 0x27, 0xDA, 0xC6,
0xD5, 0x74, 0xD1, 0xAF, 0xE9, 0x17, 0x31, 0xF4, 0x61, 0xBC, 0xDB, 0x52, 0xD7, 0x62, 0x86, 0x78,
0xD9, 0x24, 0x37, 0x5E, 0xF4, 0xC0, 0x47, 0xAE, 0x51, 0xEF, 0xD4, 0xF2, 0x58, 0xBE, 0xE0, 0x8D,
0x4E, 0x7C, 0x8B, 0xEF, 0x31, 0x83, 0x6C, 0xFD, 0x8F, 0x3A, 0xBE, 0xF5, 0x07, 0xF8, 0xDB, 0x63,
0xE0, 0xEF, 0x89, 0xB3, 0xE3, 0x29, 0x59, 0x52, 0x75, 0x3A, 0x14, 0x5C, 0x8B, 0x58, 0x74, 0x58,
0xF3, 0x36, 0x7B, 0xE4, 0xBF, 0xAB, 0xB7, 0x3C, 0x9D, 0xBF, 0xD9, 0x77, 0xD8, 0xB1, 0xE5, 0xA2,
0xF9, 0x97, 0x10, 0x4E, 0xE1, 0x3C, 0x89, 0xEB, 0x89, 0x35, 0xC6, 0xDE, 0xA5, 0x19, 0xB3, 0x8A,
0x35, 0xDD, 0x42, 0x9E, 0xF4, 0x1E, 0x3B, 0x53, 0x8D, 0x29, 0xC6, 0xDD, 0x7C, 0x53, 0x6E, 0x34,
0x6C, 0x25, 0xCE, 0xE7, 0xC1, 0xED, 0x2D, 0x7E, 0xF0, 0x9F, 0x62, 0x52, 0x5F, 0x65, 0x68, 0xF5,
0x9D, 0x75, 0xE1, 0x7B, 0x18, 0x9F, 0xD9, 0x26, 0x5A, 0xED, 0x51, 0x47, 0xAE, 0xD5, 0x63, 0x72,
0x8D, 0x9F, 0xF3, 0xED, 0xE4, 0x45, 0xBE, 0xC9, 0x17, 0x39, 0xFD, 0x36, 0x27, 0x3F, 0xC6, 0x1E,
0x88, 0xE3, 0x61, 0x01, 0x76, 0xF6, 0x59, 0xFF, 0xA8, 0xFD, 0x51, 0x7D, 0xF2, 0x83, 0x6E, 0xE7,
0x9B, 0x7A, 0x78, 0x6F, 0xFC, 0xB0, 0x4C, 0xBE, 0xE9, 0x1F, 0xCE, 0xF3, 0x80, 0x7E, 0x67, 0x9F,
0xE9, 0xAF, 0xE6, 0xAF, 0x74, 0x9B, 0xF7, 0x96, 0x4D, 0xF4, 0x56, 0xEE, 0xA1, 0x86, 0xCA, 0xAF,
0x7C, 0x2A, 0x3F, 0xF9, 0x54, 0xBA, 0xD2, 0xDF, 0x8B, 0xDB, 0xF5, 0x91, 0xF4, 0xD6, 0x18, 0x66,
0xDD, 0xCE, 0x37, 0xF4, 0xCB, 0x93, 0x5F, 0x3B, 0xE3, 0xE7, 0xE8, 0x38, 0x06, 0xAD, 0x93, 0xDD,
0xF0, 0x19, 0x74, 0x0B, 0x1E, 0xBA, 0xD5, 0x87, 0xCE, 0xF4, 0xC9, 0xAF, 0x74, 0x3B, 0x5E, 0x74,
0xC8, 0x7D, 0xCF, 0xFF, 0x2F, 0xF2, 0x87, 0x7E, 0x44, 0xEB, 0x78, 0x1A, 0xFD, 0x0D, 0x9E, 0x9C,
0x3B, 0x7D, 0xA5, 0xCD, 0x36, 0xD3, 0x49, 0xB7, 0x92, 0x0F, 0x7E, 0x95, 0x4F, 0x9F, 0xA0, 0x03,
0x5F, 0x7C, 0xA0, 0x83, 0x7F, 0xB5, 0xEF, 0xF8, 0x42, 0x5B, 0xDE, 0xCA, 0x43, 0xAB, 0xBD, 0xF2,
0x13, 0x3D, 0xE4, 0xAA, 0x7C, 0xF7, 0x01, 0xE7, 0xC2, 0x7B, 0xFB, 0x3F, 0xB0, 0xF3, 0x9C, 0x74,
0x3A, 0x5E, 0xFA, 0x2B, 0x23, 0x02, 0x0F, 0xC9, 0xAA, 0xBE, 0xF2, 0xD5, 0x67, 0xA3, 0x9B, 0xF9,
0x8C, 0xBF, 0xA9, 0x4F, 0x9D, 0xA8, 0xF9, 0x54, 0xDD, 0x8E, 0x4F, 0xDF, 0x90, 0x07, 0xFD, 0x7F,
0x90, 0x1F, 0x74, 0x41, 0x07, 0xF9, 0x11, 0x3E, 0x62, 0x6A, 0x2E, 0xA3, 0xD5, 0xA7, 0xE8, 0x0F,
0x7D, 0xDE, 0xB1, 0xDD, 0xAC, 0xB5, 0xF8, 0x0D, 0x7A, 0xD1, 0xC1, 0xBF, 0xF2, 0xC5, 0x27, 0x75,
0xE6, 0x3F, 0xE9, 0x5B, 0x4C, 0xE8, 0x5B, 0x3F, 0xD1, 0x06, 0x3B, 0x32, 0x34, 0x75, 0xD5, 0x3F,
0x74, 0x07, 0xB9, 0xC6, 0xA7, 0xFF, 0xE4, 0x1B, 0x3E, 0xC2, 0x79, 0xFD, 0x37, 0xB7, 0xD9, 0xF3,
0xFC, 0xFC, 0xE3, 0xE9, 0x2D, 0xEF, 0x45, 0x63, 0x87, 0x3D, 0x70, 0xD4, 0x3B, 0x6F, 0x57, 0x0A,
0xF3, 0x09, 0xDD, 0x20, 0x43, 0x43, 0xF7, 0x20, 0x7D, 0x6F, 0xB2, 0x5A, 0xB5, 0xDF, 0xE1, 0xC7,
0x3A, 0x9C, 0xB6, 0x1F, 0x6A, 0xBF, 0xC1, 0x9B, 0xFC, 0x2B, 0xBC, 0x68, 0xE5, 0x67, 0xDB, 0x6A,
0xEE, 0x92, 0x6F, 0xFD, 0x9A, 0x7F, 0x8C, 0x2B, 0x75, 0x4D, 0x8E, 0x31, 0x16, 0xD9, 0xA8, 0xF1,
0xD0, 0x92, 0x73, 0xC8, 0x47, 0x1D, 0xB3, 0xEF, 0x9E, 0x37, 0xDF, 0x43, 0x7C, 0xE7, 0xAB, 0x7E,
0xB0, 0x0F, 0x75, 0xD7, 0x5A, 0xEE, 0xE9, 0x57, 0xF6, 0x32, 0x27, 0x0F, 0xC5, 0x2D, 0xE4, 0xAA,
0x23, 0x5E, 0x14, 0x7C, 0x3F, 0x3F, 0x3D, 0xF1, 0xEF, 0xED, 0x3C, 0xFC, 0x8B, 0x6B, 0x4F, 0xE7,
0xA7, 0x97, 0x2F, 0xFB, 0x91, 0x30, 0x74, 0x98, 0x34, 0xF5, 0xD0, 0xDA, 0xD1, 0xE8, 0x97, 0x13,
0xE1, 0x13, 0x56, 0x74, 0xCD, 0x5F, 0x72, 0xD0, 0x94, 0xD7, 0xB4, 0xF7, 0xEB, 0xFE, 0x5D, 0x6E,
0xF9, 0x17, 0x7A, 0xA3, 0xE8, 0xFE, 0x69, 0x3A, 0xD4, 0xB2, 0xA6, 0x15, 0x24, 0xAB, 0x31, 0x39,
0x75, 0xF9, 0x78, 0x08, 0x1C, 0xE9, 0x3C, 0x07, 0xE3, 0x9C, 0x67, 0xEE, 0xA4, 0xF8, 0x66, 0xCE,
0xDF, 0xA3, 0xBD, 0xA6, 0x9D, 0x5D, 0x7C, 0xAD, 0x45, 0xFA, 0xAC, 0x31, 0xEB, 0x39, 0xEA, 0x47,
0xBB, 0xE7, 0x4B, 0x9A, 0xF9, 0x8F, 0x71, 0x5D, 0x3F, 0xC6, 0xAD, 0xC7, 0xED, 0xC0, 0x3F, 0x3F,
0x3D, 0x7F, 0x82, 0x73, 0x87, 0xFB, 0xF8, 0x70, 0x07, 0xD2, 0x06, 0x78, 0xFE, 0x9C, 0x03, 0xE7,
0x02, 0xE7, 0xC2, 0xF1, 0x7B, 0x0C, 0xD0, 0x47, 0x5A, 0xE3, 0x8E, 0x03, 0x1B, 0xE5, 0xD9, 0x7F,
0x96, 0x9B, 0x3F, 0xFC, 0x3F, 0x44, 0x07, 0xBE, 0xD4, 0x93, 0xD4, 0xEC, 0x59, 0xD7, 0x86, 0xDE,
0x9A, 0xC7, 0x59, 0xFE, 0x1D, 0xBA, 0xCA, 0xFF, 0x28, 0x9D, 0xE3, 0x77, 0xF9, 0xAA, 0xFD, 0xD7,
0xA8, 0x30, 0x55, 0x64, 0xCF, 0xD7, 0xF1, 0x37, 0xCB, 0x5D, 0x3F, 0xC7, 0xAD, 0xF2, 0xB8, 0x0C,
0xF0, 0x5F, 0x9F, 0x9E, 0x3F, 0xC0, 0x77, 0xE0, 0x7C, 0xF9, 0xD8, 0x06, 0xE0, 0x0A, 0xA4, 0x01,
0x5E, 0x32, 0x90, 0xCF, 0x4A, 0x6B, 0xCA, 0x0F, 0x08, 0xF1, 0x46, 0x67, 0xFD, 0x4A, 0x0E, 0x5A,
0x78, 0xFB, 0x67, 0xAD, 0x6F, 0xF0, 0x46, 0x91, 0x6F, 0xE8, 0x97, 0xFC, 0x3F, 0xD0, 0xE6, 0x7E,
0x6A, 0x0D, 0xB3, 0x9C, 0x7C, 0xC6, 0x3C, 0x32, 0xB6, 0x99, 0x37, 0xBA, 0xE3, 0x6F, 0xCE, 0xFD,
0x6E, 0x8D, 0x52, 0xBF, 0xA6, 0x80, 0x67, 0x27, 0x03, 0xDA, 0x59, 0xFF, 0x7B, 0x14, 0x90, 0x2A,
0x97, 0xD1, 0xBF, 0x59, 0x2F, 0xFE, 0x4D, 0x38, 0x06, 0xCF, 0xE0, 0x3A, 0xF0, 0x7D, 0xF7, 0xE1,
0x4E, 0xF4, 0xE7, 0x3F, 0x4C, 0xF1, 0xA7, 0xFD, 0x2F, 0x34, 0x70, 0xFC, 0xF0, 0x7F, 0x9C, 0x62,
0x7E, 0x72, 0xB7, 0xFC, 0xA1, 0x7F, 0xE8, 0xBF, 0x91, 0x6E, 0x9E, 0x1F, 0x3F, 0xFE, 0x0F, 0x3A,
0x89, 0x90, 0x67, 0xF9, 0x76, 0x96, 0xAE, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE,
0x42, 0x60, 0x82
};




typedef HRESULT(WINAPI* FuncReset)(IDirect3DDevice9* pIDirect3DDevice9, D3DPRESENT_PARAMETERS* pPresentationParameters);
typedef HRESULT(WINAPI* FuncEndScene)(IDirect3DDevice9* pIDirect3DDevice9);
typedef LRESULT(WINAPI* FuncWndProc)(const HWND, UINT, WPARAM, LPARAM);
HWND DirectX9Hook::g_hWnd = NULL;
IDirect3D9* g_IDirect3D9;
D3DPRESENT_PARAMETERS g_d3dpp;
IDirect3DDevice9* g_pIDirect3DDevice9;
FuncReset OldReset;
FuncEndScene OldEndScene;
FuncWndProc OldWndProc;
DWORD* dwDeviceVirtualTable = NULL;
extern LRESULT ImGui_ImplWin32_WndProcHandler(HWND hWnd, UINT message, WPARAM wParam, LPARAM lParam);

struct TextureDeleter {
	void operator()(IDirect3DTexture9* p) { if (p) p->Release(); }
};

std::unique_ptr<IDirect3DTexture9, TextureDeleter> start_button_texture;
std::unique_ptr<IDirect3DTexture9, TextureDeleter> end_button_texture;
std::unique_ptr<IDirect3DTexture9, TextureDeleter> close_button_texture;

static ImFont* g_normalFont = nullptr;
static ImFont* g_largeFont = nullptr;
static ImFont* g_boldFont = nullptr;  // 添加新的加粗字体指针

static bool isSecondWindowCollapsed = true;  // 第二个窗口的折叠状态
static float secondWindowOriginalHeight = 180.0f;  // 第二个窗口的原始高度
static bool showDamageWindow = true;
static bool showSecondWindow = true;
static bool showThirdWindow = true;
static int screenWidth = 0;
static int screenHeight = 0;

static bool isStartButtonVisible = true;  // 开始按钮是否可见
static bool isEndButtonVisible = false;   // 结束按钮是否可见
static bool showFirstWindowAndBg = false;
static bool showBossDpsWindow = false;
// 在文件开头添加新的变量声明
static time_t battleStartTime = 0;  // 战斗开始时间
static bool isBattleActive = false; // 战斗状态标志
int cursorAnimFrame = 0;
float cursorAnimTimer = 0.0f;
const float cursorAnimInterval = 0.5f; // 每0.2秒切换一次
const int cursorAnimFrameCount = 2;     // 4帧动画
// 全局变量
int thereBgAnimFrame = 0;
float thereBgAnimTimer = 0.0f;
const float thereBgAnimInterval = 0.3f; // 0.5秒切换一次
// 并确保你有
extern LPDIRECT3DTEXTURE9 texture_cursor_anim1;
extern LPDIRECT3DTEXTURE9 texture_cursor_anim2;
extern LPDIRECT3DTEXTURE9 texture_cursor_anim3;
extern LPDIRECT3DTEXTURE9 texture_cursor_anim4;
LRESULT WINAPI GrkWndProc(HWND hWnd, UINT message, WPARAM wParam, LPARAM lParam)
{


	if (KeyBoard::imguiMainEnabled)
	{
		if (ImGui_ImplWin32_WndProcHandler(hWnd, message, wParam, lParam))
		{
			return TRUE;
		}
	}

	return CallWindowProc(OldWndProc, hWnd, message, wParam, lParam);
}

HRESULT WINAPI GrkReset(IDirect3DDevice9* pIDirect3DDevice9, D3DPRESENT_PARAMETERS* d3dpp)
{
	if (!pIDirect3DDevice9 || !d3dpp) {
		return E_INVALIDARG;
	}

	Memory::SetHook(false, reinterpret_cast<void**>(&OldReset), GrkReset);
	ImGui_ImplDX9_InvalidateDeviceObjects();

	HRESULT bRet = pIDirect3DDevice9->Reset(d3dpp);

	if (SUCCEEDED(bRet)) {
		ImGui_ImplDX9_CreateDeviceObjects();
	}

	Memory::SetHook(true, reinterpret_cast<void**>(&OldReset), GrkReset);
	return bRet;
}



VOID LoadButtonTextures(LPDIRECT3DDEVICE9 pDevice)
{
	if (start_button_texture || end_button_texture) {
		return;
	}

	IDirect3DTexture9* temp_texture = nullptr;
	HRESULT hr = D3DXCreateTextureFromFileInMemory(
		pDevice,
		START_BUTTON,
		sizeof(START_BUTTON),
		&temp_texture
	);

	if (SUCCEEDED(hr)) {
		start_button_texture.reset(temp_texture);
	}

	temp_texture = nullptr;
	hr = D3DXCreateTextureFromFileInMemory(
		pDevice,
		END_BUTTON,
		sizeof(END_BUTTON),
		&temp_texture
	);

	if (SUCCEEDED(hr)) {
		end_button_texture.reset(temp_texture);
	}
}
//VOID LoadCloseButtonTexture(LPDIRECT3DDEVICE9 pDevice)
//{
//	if (close_button_texture) {
//		return;
//	}
//
//	IDirect3DTexture9* temp_texture = nullptr;
//	HRESULT hr = D3DXCreateTextureFromFileInMemory(
//		pDevice,
//		CLOSE_BUTTON,
//		sizeof(CLOSE_BUTTON),
//		&temp_texture
//	);
//
//	if (SUCCEEDED(hr)) {
//		close_button_texture.reset(temp_texture);
//	}
//}

VOID LoadAllTexture(LPDIRECT3DDEVICE9 pDevice)
{
	if (DirectX9Hook::texture_cursor != nullptr || DirectX9Hook::texture_cursor_clicked != nullptr)
		return;
	// 加载普通鼠标纹理
	wchar_t pathNormalMouse[] = L"UI/Custom.img/Cursor/1";
	DirectX9Hook::texture_cursor = LoadTextureFromPath(pDevice, pathNormalMouse);
	// 加载点击鼠标纹理
	wchar_t pathClickedMouse[] = L"UI/Custom.img/Cursor/2";
	DirectX9Hook::texture_cursor_clicked = LoadTextureFromPath(pDevice, pathClickedMouse);
	// 加载动画鼠标两帧
	wchar_t pathCursorFrame1[] = L"UI/Custom.img/Cursor/1";
	wchar_t pathCursorFrame2[] = L"UI/Custom.img/Cursor/2";
	wchar_t pathCursorFrame3[] = L"UI/Custom.img/Cursor/3";
	wchar_t pathCursorFrame4[] = L"UI/Custom.img/Cursor/4";
	texture_cursor_anim1 = LoadTextureFromPath(pDevice, pathCursorFrame1);
	texture_cursor_anim2 = LoadTextureFromPath(pDevice, pathCursorFrame2);
	texture_cursor_anim3 = LoadTextureFromPath(pDevice, pathCursorFrame3);
	texture_cursor_anim4 = LoadTextureFromPath(pDevice, pathCursorFrame4);
	// 加载Boss血条纹理
	wchar_t pathBossHpBar[] = L"UI/Custom.img/BossHP/BossHPBackGround";
	DirectX9Hook::texture_bosshpbar = LoadTextureFromPath(pDevice, pathBossHpBar);
	// 加载背景纹理
	wchar_t pathBackground[] = L"UI/Custom.img/battleStats/battleStatsBackground";
	DirectX9Hook::texture_background = LoadTextureFromPath(pDevice, pathBackground);
	// 第三个窗口加载背景纹理
	wchar_t thereBackground[] = L"UI/Custom.img/battleStats/newTexture/0";
	DirectX9Hook::there_background = LoadTextureFromPath(pDevice, thereBackground);
	wchar_t thereBackground0[] = L"UI/Custom.img/battleStats/newTexture/0";
	wchar_t thereBackground1[] = L"UI/Custom.img/battleStats/newTexture/1";
	wchar_t thereBackground2[] = L"UI/Custom.img/battleStats/newTexture/2";
	wchar_t thereBackground3[] = L"UI/Custom.img/battleStats/newTexture/3";
	//wchar_t thereBackground4[] = L"UI/Custom.img/battleStats/newTexture/4";
	//wchar_t thereBackground5[] = L"UI/Custom.img/battleStats/newTexture/5";
	//wchar_t thereBackground6[] = L"UI/Custom.img/battleStats/newTexture/6";
	//wchar_t thereBackground7[] = L"UI/Custom.img/battleStats/newTexture/7";
	DirectX9Hook::there_background_anim0 = LoadTextureFromPath(pDevice, thereBackground0);
	DirectX9Hook::there_background_anim1 = LoadTextureFromPath(pDevice, thereBackground1);
	DirectX9Hook::there_background_anim2 = LoadTextureFromPath(pDevice, thereBackground2);
	DirectX9Hook::there_background_anim3 = LoadTextureFromPath(pDevice, thereBackground3);
	//DirectX9Hook::there_background_anim4 = LoadTextureFromPath(pDevice, thereBackground4);
	//DirectX9Hook::there_background_anim5 = LoadTextureFromPath(pDevice, thereBackground5);
	//DirectX9Hook::there_background_anim6 = LoadTextureFromPath(pDevice, thereBackground6);
	//DirectX9Hook::there_background_anim7 = LoadTextureFromPath(pDevice, thereBackground7);

	//BOSSHP DPS按钮
	wchar_t buttonBackgroundPath[] = L"UI/Custom.img/BossHP/Bossdps/0"; // 你可以换成你想要的路径
	DirectX9Hook::button_background = LoadTextureFromPath(pDevice, buttonBackgroundPath);
	wchar_t buttonBackgroundHoveredPath[] = L"UI/Custom.img/BossHP/Bossdps/1";
	DirectX9Hook::button_background_hovered = LoadTextureFromPath(pDevice, buttonBackgroundHoveredPath);
	wchar_t buttonBackgroundPressedPath[] = L"UI/Custom.img/BossHP/Bossdps/2";
	DirectX9Hook::button_background_pressed = LoadTextureFromPath(pDevice, buttonBackgroundPressedPath);

	//BOSS DPS统计
	wchar_t bossDpsWindowBgPath[] = L"UI/Custom.img/BossHP/Bossdps/main/backgrnd";
	bossDpsWindowBackground = LoadTextureFromPath(pDevice, bossDpsWindowBgPath);
	//关闭按钮
	wchar_t bossDpsCloseBtnPathNormal[] = L"UI/Custom.img/BossHP/Bossdps/main/button:Close/normal";
	wchar_t bossDpsCloseBtnPathHovered[] = L"UI/Custom.img/BossHP/Bossdps/main/button:Close/mouseOver";
	wchar_t bossDpsCloseBtnPathPressed[] = L"UI/Custom.img/BossHP/Bossdps/main/button:Close/pressed";
	wchar_t bossDpsCloseBtnPathDisabled[] = L"UI/Custom.img/BossHP/Bossdps/main/button:Close/disabled";
	bossDpsCloseBtnTextureNormal = LoadTextureFromPath(pDevice, bossDpsCloseBtnPathNormal);
	bossDpsCloseBtnTextureHovered = LoadTextureFromPath(pDevice, bossDpsCloseBtnPathHovered);
	bossDpsCloseBtnTexturePressed = LoadTextureFromPath(pDevice, bossDpsCloseBtnPathPressed);
	bossDpsCloseBtnTextureDisabled = LoadTextureFromPath(pDevice, bossDpsCloseBtnPathDisabled);
	// 加载排名数字背景纹理
	wchar_t rankPath1[] = L"UI/Custom.img/BossHP/Bossdps/Ranking/1";
	wchar_t rankPath2[] = L"UI/Custom.img/BossHP/Bossdps/Ranking/2";
	wchar_t rankPath3[] = L"UI/Custom.img/BossHP/Bossdps/Ranking/3";
	wchar_t rankPath4[] = L"UI/Custom.img/BossHP/Bossdps/Ranking/4";
	wchar_t rankPath5[] = L"UI/Custom.img/BossHP/Bossdps/Ranking/5";
	wchar_t rankPath6[] = L"UI/Custom.img/BossHP/Bossdps/Ranking/6";
	wchar_t rankPath7[] = L"UI/Custom.img/BossHP/Bossdps/Ranking/7";
	wchar_t rankPath8[] = L"UI/Custom.img/BossHP/Bossdps/Ranking/8";
	wchar_t rankPath9[] = L"UI/Custom.img/BossHP/Bossdps/Ranking/9";
	wchar_t rankPath10[] = L"UI/Custom.img/BossHP/Bossdps/Ranking/10";
	DirectX9Hook::texture_rank1 = LoadTextureFromPath(pDevice, rankPath1);
	DirectX9Hook::texture_rank2 = LoadTextureFromPath(pDevice, rankPath2);
	DirectX9Hook::texture_rank3 = LoadTextureFromPath(pDevice, rankPath3);
	DirectX9Hook::texture_rank4 = LoadTextureFromPath(pDevice, rankPath4);
	DirectX9Hook::texture_rank5 = LoadTextureFromPath(pDevice, rankPath5);
	DirectX9Hook::texture_rank6 = LoadTextureFromPath(pDevice, rankPath6);
	DirectX9Hook::texture_rank7 = LoadTextureFromPath(pDevice, rankPath7);
	DirectX9Hook::texture_rank8 = LoadTextureFromPath(pDevice, rankPath8);
	DirectX9Hook::texture_rank9 = LoadTextureFromPath(pDevice, rankPath9);
	DirectX9Hook::texture_rank10 = LoadTextureFromPath(pDevice, rankPath10);
	if (!DirectX9Hook::texture_cursor || !DirectX9Hook::texture_cursor_clicked || !DirectX9Hook::texture_bosshpbar || !DirectX9Hook::texture_background || !DirectX9Hook::there_background)
	{
		if (DirectX9Hook::texture_cursor)
		{
			DirectX9Hook::texture_cursor->Release();
			DirectX9Hook::texture_cursor = nullptr;
		}
		if (DirectX9Hook::texture_cursor_clicked)
		{
			DirectX9Hook::texture_cursor_clicked->Release();
			DirectX9Hook::texture_cursor_clicked = nullptr;
		}
		if (DirectX9Hook::texture_bosshpbar)
		{
			DirectX9Hook::texture_bosshpbar->Release();
			DirectX9Hook::texture_bosshpbar = nullptr;
		}
		if (DirectX9Hook::texture_background)
		{
			DirectX9Hook::texture_background->Release();
			DirectX9Hook::texture_background = nullptr;
		}
		if (DirectX9Hook::there_background)
		{
			DirectX9Hook::there_background->Release();
			DirectX9Hook::there_background = nullptr;
		}
		// 清理排名纹理
		if (DirectX9Hook::texture_rank1)
		{
			DirectX9Hook::texture_rank1->Release();
			DirectX9Hook::texture_rank1 = nullptr;
		}
		if (DirectX9Hook::texture_rank2)
		{
			DirectX9Hook::texture_rank2->Release();
			DirectX9Hook::texture_rank2 = nullptr;
		}
		if (DirectX9Hook::texture_rank3)
		{
			DirectX9Hook::texture_rank3->Release();
			DirectX9Hook::texture_rank3 = nullptr;
		}
		if (DirectX9Hook::texture_rank4)
		{
			DirectX9Hook::texture_rank4->Release();
			DirectX9Hook::texture_rank4 = nullptr;
		}
		if (DirectX9Hook::texture_rank5)
		{
			DirectX9Hook::texture_rank5->Release();
			DirectX9Hook::texture_rank5 = nullptr;
		}
		if (DirectX9Hook::texture_rank6)
		{
			DirectX9Hook::texture_rank6->Release();
			DirectX9Hook::texture_rank6 = nullptr;
		}
		if (DirectX9Hook::texture_rank7)
		{
			DirectX9Hook::texture_rank7->Release();
			DirectX9Hook::texture_rank7 = nullptr;
		}
		if (DirectX9Hook::texture_rank8)
		{
			DirectX9Hook::texture_rank8->Release();
			DirectX9Hook::texture_rank8 = nullptr;
		}
		if (DirectX9Hook::texture_rank9)
		{
			DirectX9Hook::texture_rank9->Release();
			DirectX9Hook::texture_rank9 = nullptr;
		}
		if (DirectX9Hook::texture_rank10)
		{
			DirectX9Hook::texture_rank10->Release();
			DirectX9Hook::texture_rank10 = nullptr;
		}
	}
}

HRESULT WINAPI GrkEndScene(IDirect3DDevice9* pIDirect3DDevice9)
{
	Memory::SetHook(false, reinterpret_cast<void**>(&OldEndScene), GrkEndScene);
	static bool bFlag = TRUE;
	if (bFlag)
	{
		bFlag = FALSE;
		IMGUI_CHECKVERSION();
		ImGui::CreateContext();
		LoadAllTexture(pIDirect3DDevice9);
		ImGuiIO& io = ImGui::GetIO();

		// 创建普通字体
		io.Fonts->AddFontFromMemoryTTF((void*)baidu_font_data, baidu_font_size, 13.0f, NULL,
			io.Fonts->GetGlyphRangesChineseFull());
		ImFontConfig boldConfig;
		boldConfig.FontDataOwnedByAtlas = false;
		boldConfig.RasterizerMultiply = 2.0f;  // 增加加粗程度
		g_boldFont = io.Fonts->AddFontFromMemoryTTF((void*)baidu_font_data, baidu_font_size, 13.0f, &boldConfig,
			io.Fonts->GetGlyphRangesChineseFull());
		// 添加这行来支持中文字符
		io.Fonts->AddFontFromMemoryTTF((void*)baidu_font_data, baidu_font_size, 13.0f, NULL,
			io.Fonts->GetGlyphRangesChineseFull());  // 使用 GetGlyphRangesChineseFull 替代 GetGlyphRangesChineseSimplifiedCommon



		// 加载按钮纹理
		LoadButtonTextures(pIDirect3DDevice9);



		io.ConfigFlags = ImGuiConfigFlags_NoMouseCursorChange;
		io.WantSaveIniSettings = false;
		io.IniFilename = NULL;

		// 设置深色主题
		ImGuiStyle& style = ImGui::GetStyle();
		style.Colors[ImGuiCol_WindowBg] = ImVec4(0.10f, 0.10f, 0.10f, 0.94f);
		style.Colors[ImGuiCol_TitleBg] = ImVec4(0.0f, 0.35f, 0.60f, 1.0f);
		style.Colors[ImGuiCol_TitleBgActive] = ImVec4(0.0f, 0.45f, 0.70f, 1.0f);
		style.Colors[ImGuiCol_TitleBgCollapsed] = ImVec4(0.0f, 0.35f, 0.60f, 0.8f);
		style.Colors[ImGuiCol_Text] = ImVec4(1.0f, 1.0f, 1.0f, 1.0f);
		style.Colors[ImGuiCol_Border] = ImVec4(0.3f, 0.3f, 0.3f, 1.0f);
		style.Colors[ImGuiCol_TableHeaderBg] = ImVec4(0.15f, 0.15f, 0.15f, 1.0f);
		style.Colors[ImGuiCol_TableBorderStrong] = ImVec4(0.3f, 0.3f, 0.3f, 1.0f);
		style.Colors[ImGuiCol_TableBorderLight] = ImVec4(0.2f, 0.2f, 0.2f, 1.0f);
		style.Colors[ImGuiCol_TableRowBg] = ImVec4(0.1f, 0.1f, 0.1f, 1.0f);
		style.Colors[ImGuiCol_TableRowBgAlt] = ImVec4(0.15f, 0.15f, 0.15f, 1.0f);

		// 设置窗口样式
		style.WindowBorderSize = 1.0f;
		style.WindowRounding = 8.0f;         // 窗口圆角
		style.ChildRounding = 8.0f;          // 子窗口圆角
		style.FrameRounding = 4.0f;          // 框架圆角(按钮等)
		style.PopupRounding = 8.0f;          // 弹出窗口圆角
		style.ScrollbarRounding = 4.0f;      // 滚动条圆角
		style.GrabRounding = 4.0f;           // 滑块圆角
		style.TabRounding = 4.0f;            // 标签页圆角
		style.WindowPadding = ImVec2(5, 5);
		style.FramePadding = ImVec2(5, 5);
		style.ItemSpacing = ImVec2(4, 4);
		style.ScrollbarSize = 15.0f;

		// 在初始化时创建字体
		ImFontConfig imfConfig;
		imfConfig.FontDataOwnedByAtlas = false;
		g_normalFont = io.Fonts->AddFontFromMemoryTTF((void*)baidu_font_data, baidu_font_size, 13.0f, &imfConfig, io.Fonts->GetGlyphRangesChineseSimplifiedCommon());
		//下方是改变第二个窗体文字大小
		g_largeFont = io.Fonts->AddFontFromMemoryTTF((void*)baidu_font_data, baidu_font_size, 15.0f, &imfConfig, io.Fonts->GetGlyphRangesChineseSimplifiedCommon());
		// 设置表头颜色为透明
		style.Colors[ImGuiCol_TableHeaderBg] = ImVec4(0.0f, 0.0f, 0.0f, 0.0f);
		style.Colors[ImGuiCol_Header] = ImVec4(0.0f, 0.0f, 0.0f, 0.0f);
		style.Colors[ImGuiCol_HeaderHovered] = ImVec4(0.0f, 0.0f, 0.0f, 0.0f);
		style.Colors[ImGuiCol_HeaderActive] = ImVec4(0.0f, 0.0f, 0.0f, 0.0f);

		ImGui_ImplWin32_Init(DirectX9Hook::GetWindowHandle());
		ImGui_ImplDX9_Init(pIDirect3DDevice9);
		OldWndProc = (WNDPROC)SetWindowLongPtr(DirectX9Hook::GetWindowHandle(), GWL_WNDPROC, (LONG_PTR)GrkWndProc);
	}

	HRESULT hRet = pIDirect3DDevice9->EndScene();

	// 开始新的 ImGui 帧
	ImGui_ImplDX9_NewFrame();
	ImGui_ImplWin32_NewFrame();
	ImGui::NewFrame();
	// === 这里插入自动BOSSDPS控制代码 ===
// 自动BOSSDPS控制
	if (BossHP::dBossHpPercentage > 0) {
		if (!Bodyrelmove::isBossDpsActive) {
			Bodyrelmove::isBossDpsActive = true;
			Bodyrelmove::bossDpsStartTime = time(0);
			Bodyrelmove::ClearBossDpsData();
		}
	}
	else {
		if (Bodyrelmove::isBossDpsActive) {
			Bodyrelmove::isBossDpsActive = false;
			Bodyrelmove::bossDpsStartTime = 0;
			// 这里可以弹窗或保存BOSSDPS数据
		}
	}

	// BOSS DPS按钮窗口
	if (BossHP::dBossHpPercentage > 0 && DirectX9Hook::button_background)
	{
		D3DSURFACE_DESC btn_desc;
		DirectX9Hook::button_background->GetLevelDesc(0, &btn_desc);
		ImVec2 btn_size((float)btn_desc.Width, (float)btn_desc.Height);

		ImVec2 window_pos(453, 19.2f);
		ImGui::SetNextWindowPos(window_pos);
		ImGui::SetNextWindowSize(btn_size);

		ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(0, 0));
		ImGui::PushStyleColor(ImGuiCol_WindowBg, ImVec4(0, 0, 0, 0));
		ImGui::PushStyleColor(ImGuiCol_Border, ImVec4(0, 0, 0, 0));

		if (ImGui::Begin("##BossButtonWindow", nullptr,
			ImGuiWindowFlags_NoTitleBar |
			ImGuiWindowFlags_NoResize |
			ImGuiWindowFlags_NoMove |
			ImGuiWindowFlags_NoScrollbar |
			ImGuiWindowFlags_NoScrollWithMouse))
		{
			ImVec2 p = ImGui::GetWindowPos();
			ImGui::SetCursorPos(ImVec2(0, 0));
			if (ImGui::InvisibleButton("BossButton", btn_size)) {
				showBossDpsWindow = !showBossDpsWindow;
			}
			ImDrawList* draw_list = ImGui::GetWindowDrawList();

			// 优先级：按下 > 悬停 > 默认
			LPDIRECT3DTEXTURE9 tex = DirectX9Hook::button_background;
			if (ImGui::IsItemActive() && DirectX9Hook::button_background_pressed)
				tex = DirectX9Hook::button_background_pressed;
			else if (ImGui::IsItemHovered() && DirectX9Hook::button_background_hovered)
				tex = DirectX9Hook::button_background_hovered;

			draw_list->AddImage(
				tex,
				p,
				ImVec2(p.x + btn_size.x, p.y + btn_size.y)
			);
		}
		ImGui::End();

		ImGui::PopStyleColor(2);
		ImGui::PopStyleVar();
	}

	//BOSS DPS统计
	// === 你的新窗口代码应该放在这里，和上面同级 ===
	if (showBossDpsWindow && bossDpsWindowBackground)
	{
		D3DSURFACE_DESC desc;
		bossDpsWindowBackground->GetLevelDesc(0, &desc);
		ImVec2 win_size((float)desc.Width, (float)desc.Height);

		ImGui::SetNextWindowPos(ImVec2(1020, 230)); // 你可以自定义位置
		ImGui::SetNextWindowSize(win_size);
		ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(0, 0));
		ImGui::PushStyleColor(ImGuiCol_WindowBg, ImVec4(0, 0, 0, 0));
		ImGui::PushStyleColor(ImGuiCol_Border, ImVec4(0, 0, 0, 0));


		if (ImGui::Begin("##BossDpsWindow", nullptr, ImGuiWindowFlags_NoTitleBar | ImGuiWindowFlags_NoResize | ImGuiWindowFlags_NoMove))

		{
			ImDrawList* draw_list = ImGui::GetWindowDrawList();
			ImVec2 p = ImGui::GetWindowPos();
			draw_list->AddImage(
				bossDpsWindowBackground,
				p,
				ImVec2(p.x + win_size.x, p.y + win_size.y)
			);
			// 关闭按钮
			if (bossDpsCloseBtnTextureNormal)
			{
				ImVec2 btn_size(24, 24); // 按实际图片大小调整
				ImVec2 win_size = ImGui::GetWindowSize();
				ImVec2 btn_pos = ImVec2(win_size.x - btn_size.x - 12, 3);

				ImGui::SetCursorPos(btn_pos);
				ImGui::InvisibleButton("BossDpsCloseBtn", btn_size);

				LPDIRECT3DTEXTURE9 closeTex = bossDpsCloseBtnTextureNormal;

				// 这里可以根据你的逻辑判断是否不可用
				bool isDisabled = false; // 你可以根据实际情况设置

				if (isDisabled && bossDpsCloseBtnTextureDisabled) {
					closeTex = bossDpsCloseBtnTextureDisabled;
				}
				else if (ImGui::IsItemActive() && bossDpsCloseBtnTexturePressed) {
					closeTex = bossDpsCloseBtnTexturePressed;
				}
				else if (ImGui::IsItemHovered() && bossDpsCloseBtnTextureHovered) {
					closeTex = bossDpsCloseBtnTextureHovered;
				}

				ImDrawList* draw_list = ImGui::GetWindowDrawList();
				ImVec2 p = ImGui::GetItemRectMin();
				draw_list->AddImage(
					closeTex,
					p,
					ImVec2(p.x + btn_size.x, p.y + btn_size.y)
				);

				if (!isDisabled && ImGui::IsItemDeactivated() && ImGui::IsItemHovered()) {
					showBossDpsWindow = false;
				}

				ImGui::PopStyleColor(3);
			}
			// 这里可以添加你想要的内容
			// ==== BOSSDPS数据排行 ====
			std::vector<std::pair<std::string, AttackData>> sortedBossData(
				Bodyrelmove::bossDpsDataMap.begin(), Bodyrelmove::bossDpsDataMap.end());
			std::sort(sortedBossData.begin(), sortedBossData.end(),
				[](const std::pair<std::string, AttackData>& a, const std::pair<std::string, AttackData>& b) {
					return a.second.totalDamage > b.second.totalDamage;
				});

			// 计算总伤害
			long long totalDamage = 0;
			for (const auto& pair : sortedBossData) {
				totalDamage += pair.second.totalDamage;
			}

			// 显示排行榜
			int rank = 1;

			// 整体下移和右移
			ImGui::Dummy(ImVec2(0.0f, 18.0f));
			ImGui::Indent(48.0f);//显示名字的左右移动

			for (const auto& pair : sortedBossData) {
				const auto& data = pair.second;
				float percent = (totalDamage > 0) ? (100.0f * data.totalDamage / totalDamage) : 0.0f;

				// 获取当前行的起始位置
				ImVec2 lineStartPos = ImGui::GetCursorPos();

				// 绘制排名数字背景纹理
				LPDIRECT3DTEXTURE9 rankTexture = nullptr;
				switch (rank) {
				case 1: rankTexture = DirectX9Hook::texture_rank1; break;
				case 2: rankTexture = DirectX9Hook::texture_rank2; break;
				case 3: rankTexture = DirectX9Hook::texture_rank3; break;
				case 4: rankTexture = DirectX9Hook::texture_rank4; break;
				case 5: rankTexture = DirectX9Hook::texture_rank5; break;
				case 6: rankTexture = DirectX9Hook::texture_rank6; break;
				case 7: rankTexture = DirectX9Hook::texture_rank7; break;
				case 8: rankTexture = DirectX9Hook::texture_rank8; break;
				case 9: rankTexture = DirectX9Hook::texture_rank9; break;
				case 10: rankTexture = DirectX9Hook::texture_rank10; break;
				default: rankTexture = nullptr; break;
				}

				if (rankTexture) {
					ImDrawList* draw_list = ImGui::GetWindowDrawList();
					ImVec2 windowPos = ImGui::GetWindowPos();
					ImVec2 rankPos = ImVec2(windowPos.x + 15, windowPos.y + lineStartPos.y + 2); // 固定X位置为12像素
					ImVec2 rankSize(20, 14); // 排名图标大小，可以调整

					draw_list->AddImage(
						rankTexture,
						rankPos,
						ImVec2(rankPos.x + rankSize.x, rankPos.y + rankSize.y)
					);
				}

				// 显示名称 - 添加编码转换
				std::string utf8_name = GBKToUTF8(data.roleName);
				ImGui::PushFont(g_boldFont); // 使用加粗字体
				ImGui::Text("%s", utf8_name.c_str());
				ImGui::PopFont();

				// 伤害 - 在固定位置
				ImGui::SameLine(130.0f); // 调整这个值来控制伤害的起始位置 数值越小往左移动

				// 格式化伤害数值
				std::string formattedDamage;
				if (data.totalDamage >= 100000000) { // 1亿以上
					double value = data.totalDamage / 100000000.0;
					char buffer[32];
					sprintf_s(buffer, "%.2f", value);
					formattedDamage = std::string(buffer) + "亿";
				}
				else if (data.totalDamage >= 10000) { // 1万以上
					double value = data.totalDamage / 10000.0;
					char buffer[32];
					sprintf_s(buffer, "%.2f", value);
					formattedDamage = std::string(buffer) + "万";
				}
				else { // 1万以下直接显示
					char buffer[32];
					sprintf_s(buffer, "%lld", data.totalDamage);
					formattedDamage = buffer;
				}

				// 转换为UTF-8编码
				std::string utf8_damage = GBKToUTF8(formattedDamage);
				//ImGui::Text("%s", utf8_damage.c_str());

				// 伤害右对齐显示 - 个位数固定在最右侧
				ImGui::PushFont(g_boldFont); // 使用加粗字体
				ImGui::Text("%s", utf8_damage.c_str()); // 显示伤害数值
				ImGui::PopFont();

				// 百分比 - 在固定位置
				ImGui::SameLine(213.0f); // 调整这个值来控制百分比的起始位置
				ImGui::PushFont(g_boldFont); // 使用加粗字体
				ImGui::Text("%.0f%%", percent); // 不显示小数点后两位
				ImGui::PopFont();

				// 添加行间距 - 调整这个值来控制每个玩家之间的间距
				ImGui::Dummy(ImVec2(0.0f, 3.0f)); // 3像素的间距，可以调整这个值
				// 递增排名
				rank++;
			}

			ImGui::Unindent(50.0f);
		}
		ImGui::End();

		ImGui::PopStyleColor(2);
		ImGui::PopStyleVar();
	}
	if (KeyBoard::imguiMainEnabled)
	{
		// 获取当前时间
		time_t now = time(0);
		tm* ltm = localtime(&now);
		char timeStr[32];
		sprintf_s(timeStr, "%04d-%02d-%02d %02d:%02d:%02d",
			1900 + ltm->tm_year,
			1 + ltm->tm_mon,
			ltm->tm_mday,
			ltm->tm_hour,
			ltm->tm_min,
			ltm->tm_sec);

		// 如果背景纹理存在，绘制背景
		if (showFirstWindowAndBg && DirectX9Hook::texture_background != nullptr) {

			ImVec2 window_pos(1072, 51);
			float currentHeight = 182.0f;

			// 使用图片的实际分辨率
			float bgWidth = 211.0f;   // 原始宽度
			float bgHeight = 182.0f;  // 原始高度

			// 计算显示尺寸，保持原始比例
			// 使用精确的像素尺寸，避免小数点导致的模糊
			float displayWidth = bgWidth;  // 使用原始宽度
			float displayHeight = bgHeight;  // 使用原始高度

			ImVec2 window_size(displayWidth, displayHeight);

			// 计算UV坐标，使用精确值
			float uvY = 1.0f;

			// 在绘制窗口之前，修改背景图片的绘制代码
			ImDrawList* draw_list = ImGui::GetBackgroundDrawList();

			// 计算背景图片的透明度
			float bgAlpha = Bodyrelmove::fadeAlpha;

			// 只在有透明度时才绘制背景
			if (bgAlpha > 0.0f) {
				draw_list->AddImageRounded(
					DirectX9Hook::texture_background,
					window_pos,
					ImVec2(window_pos.x + displayWidth, window_pos.y + displayHeight),
					ImVec2(0, 0),
					ImVec2(1, uvY),
					IM_COL32(255, 255, 255, (int)(bgAlpha * 255)),
					8.0f
				);
			}

			// 设置窗口位置和大小
			ImGui::SetNextWindowPos(ImVec2(1072, 51));
			ImGui::SetNextWindowSize(ImVec2(211, 182.0f));

			// 只在有透明度时才渲染窗口内容
			if (bgAlpha > 0.0f && showDamageWindow) { // 加上 showDamageWindow 判断
				ImGui::PushStyleVar(ImGuiStyleVar_Alpha, bgAlpha);
				ImGui::PushStyleColor(ImGuiCol_WindowBg, ImVec4(0.0f, 0.0f, 0.0f, 0.0f));
				ImGui::PushStyleColor(ImGuiCol_Border, ImVec4(0.0f, 0.0f, 0.0f, 0.0f));

				if (ImGui::Begin("##DamageWindow", nullptr,
					ImGuiWindowFlags_NoTitleBar |
					ImGuiWindowFlags_NoResize |
					ImGuiWindowFlags_NoMove))
				{
					// 在右上角添加关闭按钮（支持4状态）
					if (bossDpsCloseBtnTextureNormal)
					{
						ImVec2 btn_size(20, 20); // 按实际图片大小调整
						ImVec2 win_size = ImGui::GetWindowSize();
						ImVec2 btn_pos = ImVec2(win_size.x - btn_size.x - 8, 8); // 右上角，边距可调

						ImGui::SetCursorPos(btn_pos);
						ImGui::InvisibleButton("DamageWindowCloseBtn", btn_size);

						LPDIRECT3DTEXTURE9 closeTex = bossDpsCloseBtnTextureNormal;
						bool isDisabled = false; // 如需禁用可自定义

						if (isDisabled && bossDpsCloseBtnTextureDisabled) {
							closeTex = bossDpsCloseBtnTextureDisabled;
						}
						else if (ImGui::IsItemActive() && bossDpsCloseBtnTexturePressed) {
							closeTex = bossDpsCloseBtnTexturePressed;
						}
						else if (ImGui::IsItemHovered() && bossDpsCloseBtnTextureHovered) {
							closeTex = bossDpsCloseBtnTextureHovered;
						}

						ImDrawList* draw_list = ImGui::GetWindowDrawList();
						ImVec2 p = ImGui::GetItemRectMin();
						draw_list->AddImage(
							closeTex,
							p,
							ImVec2(p.x + btn_size.x, p.y + btn_size.y)
						);

						// 只在鼠标松开且还在按钮上时关闭
						if (!isDisabled && ImGui::IsItemDeactivated() && ImGui::IsItemHovered()) {
							showDamageWindow = false; // 只关闭自己
							showFirstWindowAndBg = false; // 关闭背景
							ShowCursor(TRUE);
						}
					}




					// 显示内容 - 移除折叠判断
					std::vector<std::pair<std::string, AttackData>> sortedData;
					if (!Bodyrelmove::attackDataMap.empty()) {
						sortedData.push_back(*Bodyrelmove::attackDataMap.begin());
					}

					// 按总伤害从高到低排序
					std::sort(sortedData.begin(), sortedData.end(),
						[](const auto& a, const auto& b) {
							return a.second.totalDamage > b.second.totalDamage;
						});

					// 在BeginTable之前添加表格样式设置
					ImGui::PushStyleColor(ImGuiCol_TableBorderStrong, ImVec4(0.0f, 0.0f, 0.0f, 0.0f));  // 表格边框透明
					ImGui::PushStyleColor(ImGuiCol_TableBorderLight, ImVec4(0.0f, 0.0f, 0.0f, 0.0f));   // 表格边框透明
					ImGui::PushStyleColor(ImGuiCol_TableRowBg, ImVec4(0.0f, 0.0f, 0.0f, 0.0f));         // 表格行背景透明
					ImGui::PushStyleColor(ImGuiCol_TableRowBgAlt, ImVec4(0.0f, 0.0f, 0.0f, 0.0f));      // 交替行背景透明

					// 添加攻击数据表格
					if (ImGui::BeginTable("AttackDataTable", 1,
						ImGuiTableFlags_Borders | ImGuiTableFlags_RowBg | ImGuiTableFlags_ScrollY))
					{
						// 使用大号字体显示内容
						ImGui::PushFont(g_largeFont);

						if (!sortedData.empty()) {
							const auto& data = sortedData[0].second;

							// 名称行
							ImGui::TableNextRow();
							ImGui::TableSetColumnIndex(0);
							ImGui::SetCursorPosX(ImGui::GetCursorPosX() + 91);
							ImGui::SetCursorPosY(ImGui::GetCursorPosY() + 18); // 向上偏移
							std::string utf8_name = GBKToUTF8(data.roleName);

							// 在名称上方显示战斗时间
							if (isBattleActive && battleStartTime > 0) {
								time_t currentTime = time(0);
								time_t elapsedTime = currentTime - battleStartTime;

								int hours = static_cast<int>(elapsedTime / 3600);
								int minutes = static_cast<int>((elapsedTime % 3600) / 60);
								int seconds = static_cast<int>(elapsedTime % 60);

								char timeStr[32];
								sprintf_s(timeStr, "%02d:%02d:%02d", hours, minutes, seconds);

								ImGui::PushFont(g_boldFont);
								float textWidth = ImGui::CalcTextSize(timeStr).x;
								ImGui::SetCursorPosY(ImGui::GetCursorPosY() + -5);
								ImGui::SetCursorPosX(ImGui::GetCursorPosX() + 91 - textWidth);
								ImGui::TextColored(ImVec4(210.0f / 255.0f, 255.0f / 255.0f, 170.0f / 255.0f, 1.0f), "%s", timeStr);
								ImGui::PopFont();
							}
							else {
								ImGui::PushFont(g_boldFont);
								float textWidth = ImGui::CalcTextSize("00:00:00").x;
								ImGui::SetCursorPosX(ImGui::GetCursorPosX() + 91 - textWidth);
								ImGui::TextColored(ImVec4(210.0f / 255.0f, 255.0f / 255.0f, 170.0f / 255.0f, 1.0f), "00:00:00");
								ImGui::PopFont();
							}

							// 输出行（伤害）
							ImGui::TableNextRow();
							ImGui::TableSetColumnIndex(0);
							ImGui::SetCursorPosX(ImGui::GetCursorPosX() + 91);
							ImGui::SetCursorPosY(ImGui::GetCursorPosY() + 6);
							char formattedDamage[32];
							sprintf_s(formattedDamage, "%lld", data.totalDamage);
							std::string damageStr = formattedDamage;
							int len = damageStr.length();
							for (int i = len - 3; i > 0; i -= 3) {
								damageStr.insert(i, ",");
							}
							ImGui::PushFont(g_boldFont);
							float textWidth = ImGui::CalcTextSize(damageStr.c_str()).x;
							ImGui::SetCursorPosX(ImGui::GetCursorPosX() + 91 - textWidth);
							ImGui::TextColored(ImVec4(210.0f / 255.0f, 255.0f / 255.0f, 170.0f / 255.0f, 1.0f), "%s", damageStr.c_str());
							ImGui::PopFont();

							// 金币行 - 移到这里，在伤害后面
							ImGui::TableNextRow();
							ImGui::TableSetColumnIndex(0);
							ImGui::SetCursorPosX(ImGui::GetCursorPosX() + 91);
							ImGui::SetCursorPosY(ImGui::GetCursorPosY() + 1);
							if (data.totalMeso > 0) {
								char formattedMeso[32];
								sprintf_s(formattedMeso, "%lld", data.totalMeso);
								std::string mesoStr = formattedMeso;
								len = mesoStr.length();
								for (int i = len - 3; i > 0; i -= 3) {
									mesoStr.insert(i, ",");
								}
								ImGui::PushFont(g_boldFont);
								textWidth = ImGui::CalcTextSize(mesoStr.c_str()).x;
								ImGui::SetCursorPosX(ImGui::GetCursorPosX() + 91 - textWidth);
								ImGui::TextColored(ImVec4(210.0f / 255.0f, 255.0f / 255.0f, 170.0f / 255.0f, 1.0f), "%s", mesoStr.c_str());
								ImGui::PopFont();
							}
							else {
								ImGui::PushFont(g_boldFont);
								textWidth = ImGui::CalcTextSize("0").x;
								ImGui::SetCursorPosX(ImGui::GetCursorPosX() + 91 - textWidth);
								ImGui::TextColored(ImVec4(210.0f / 255.0f, 255.0f / 255.0f, 170.0f / 255.0f, 1.0f), "0");
								ImGui::PopFont();
							}

							// 狩猎经验行 - 移到金币后面
							ImGui::TableNextRow();
							ImGui::TableSetColumnIndex(0);
							ImGui::SetCursorPosX(ImGui::GetCursorPosX() + 91);
							ImGui::SetCursorPosY(ImGui::GetCursorPosY() + 3);
							if (data.totalExp > 0) {
								char formattedExp[32];
								sprintf_s(formattedExp, "%u", data.totalExp);
								std::string expStr = formattedExp;
								len = expStr.length();
								for (int i = len - 3; i > 0; i -= 3) {
									expStr.insert(i, ",");
								}
								ImGui::PushFont(g_boldFont);
								textWidth = ImGui::CalcTextSize(expStr.c_str()).x;
								ImGui::SetCursorPosX(ImGui::GetCursorPosX() + 91 - textWidth);
								ImGui::TextColored(ImVec4(210.0f / 255.0f, 255.0f / 255.0f, 170.0f / 255.0f, 1.0f), "%s", expStr.c_str());
								ImGui::PopFont();
							}
							else {
								ImGui::PushFont(g_boldFont);
								textWidth = ImGui::CalcTextSize("0").x;
								ImGui::SetCursorPosX(ImGui::GetCursorPosX() + 91 - textWidth);
								ImGui::TextColored(ImVec4(210.0f / 255.0f, 255.0f / 255.0f, 170.0f / 255.0f, 1.0f), "0");
								ImGui::PopFont();
							}
						}
						else {
							// 在没有数据时的显示部分
							// 战斗时间行
							ImGui::TableNextRow();
							ImGui::TableSetColumnIndex(0);
							ImGui::SetCursorPosX(ImGui::GetCursorPosX() + 91);
							ImGui::SetCursorPosY(ImGui::GetCursorPosY() + 13);
							if (isBattleActive && battleStartTime > 0) {
								time_t currentTime = time(0);
								time_t elapsedTime = currentTime - battleStartTime;

								int hours = static_cast<int>(elapsedTime / 3600);
								int minutes = static_cast<int>((elapsedTime % 3600) / 60);
								int seconds = static_cast<int>(elapsedTime % 60);

								char timeStr[32];
								sprintf_s(timeStr, "%02d:%02d:%02d", hours, minutes, seconds);

								ImGui::PushFont(g_boldFont);
								float textWidth = ImGui::CalcTextSize(timeStr).x;
								ImGui::SetCursorPosX(ImGui::GetCursorPosX() + 91 - textWidth);
								ImGui::TextColored(ImVec4(210.0f / 255.0f, 255.0f / 255.0f, 170.0f / 255.0f, 1.0f), "%s", timeStr);
								ImGui::PopFont();
							}
							else {
								ImGui::PushFont(g_boldFont);
								float textWidth = ImGui::CalcTextSize("00:00:00").x;
								ImGui::SetCursorPosX(ImGui::GetCursorPosX() + 91 - textWidth);
								ImGui::TextColored(ImVec4(210.0f / 255.0f, 255.0f / 255.0f, 170.0f / 255.0f, 1.0f), "00:00:00");
								ImGui::PopFont();
							}

							// 输出行（伤害）
							ImGui::TableNextRow();
							ImGui::TableSetColumnIndex(0);
							ImGui::SetCursorPosX(ImGui::GetCursorPosX() + 91);
							ImGui::SetCursorPosY(ImGui::GetCursorPosY() + 6);
							ImGui::PushFont(g_boldFont);
							float textWidth = ImGui::CalcTextSize("0").x;
							ImGui::SetCursorPosX(ImGui::GetCursorPosX() + 91 - textWidth);
							ImGui::TextColored(ImVec4(210.0f / 255.0f, 255.0f / 255.0f, 170.0f / 255.0f, 1.0f), "0");
							ImGui::PopFont();

							// 金币行 - 移到这里，在伤害后面
							ImGui::TableNextRow();
							ImGui::TableSetColumnIndex(0);
							ImGui::SetCursorPosX(ImGui::GetCursorPosX() + 91);
							ImGui::SetCursorPosY(ImGui::GetCursorPosY() + 1);
							ImGui::PushFont(g_boldFont);
							textWidth = ImGui::CalcTextSize("0").x;
							ImGui::SetCursorPosX(ImGui::GetCursorPosX() + 91 - textWidth);
							ImGui::TextColored(ImVec4(210.0f / 255.0f, 255.0f / 255.0f, 170.0f / 255.0f, 1.0f), "0");
							ImGui::PopFont();

							// 经验行 - 移到金币后面
							ImGui::TableNextRow();
							ImGui::TableSetColumnIndex(0);
							ImGui::SetCursorPosX(ImGui::GetCursorPosX() + 91);
							ImGui::SetCursorPosY(ImGui::GetCursorPosY() + 3);
							ImGui::PushFont(g_boldFont);
							textWidth = ImGui::CalcTextSize("0").x;
							ImGui::SetCursorPosX(ImGui::GetCursorPosX() + 91 - textWidth);
							ImGui::TextColored(ImVec4(210.0f / 255.0f, 255.0f / 255.0f, 170.0f / 255.0f, 1.0f), "0");
							ImGui::PopFont();
						}

						ImGui::PopFont();

						ImGui::EndTable();
					}

					// 加载按钮纹理
					LoadButtonTextures(pIDirect3DDevice9);

					// 设置按钮位置
					ImGui::SetCursorPos(ImVec2(10, 136));  // 调整起始位置以适应更大的按钮

					// 创建按钮容器 - 移除滚动条
					if (ImGui::BeginChild("ButtonContainer", ImVec2(191, 36), false,
						ImGuiWindowFlags_NoScrollbar | ImGuiWindowFlags_NoScrollWithMouse))
					{
						// 移除所有可能的边框和背景
						ImGui::PushStyleColor(ImGuiCol_Border, ImVec4(0, 0, 0, 0));
						ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0, 0, 0, 0));
						ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0, 0, 0, 0));
						ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(0, 0, 0, 0));
						ImGui::PushStyleVar(ImGuiStyleVar_FrameBorderSize, 0.0f);
						ImGui::PushStyleVar(ImGuiStyleVar_FramePadding, ImVec2(0, 0));

						// 开始按钮
						if (isStartButtonVisible && start_button_texture)
						{
							if (ImGui::ImageButton("StartButton", start_button_texture.get(),
								ImVec2(191, 36), ImVec2(0, 0), ImVec2(1, 1), ImVec4(0, 0, 0, 0)))
							{
								isStartButtonVisible = false;
								isEndButtonVisible = true;
								battleStartTime = time(0);
								isBattleActive = true;
								Bodyrelmove::isBattleActive = true;

								// 清除之前的金币数据
								for (auto& pair : Bodyrelmove::attackDataMap) {
									pair.second.totalMeso = 0;
								}
							}
						}

						// 结束按钮
						if (isEndButtonVisible && end_button_texture)
						{
							if (ImGui::ImageButton("EndButton", end_button_texture.get(),
								ImVec2(191, 36), ImVec2(0, 0), ImVec2(1, 1), ImVec4(0, 0, 0, 0)))
							{
								isStartButtonVisible = true;
								isEndButtonVisible = false;
								isBattleActive = false;
								battleStartTime = 0;
								Bodyrelmove::isBattleActive = false;

								// 清除所有数据，包括金币数据
								Bodyrelmove::attackDataMap.clear();
							}
						}

						// 恢复样式
						ImGui::PopStyleVar(2);  // 恢复两个 StyleVar
						ImGui::PopStyleColor(4);  // 恢复四个 StyleColor

						ImGui::EndChild();
					}
				}
				ImGui::End();

				ImGui::PopStyleColor(2);
				ImGui::PopStyleVar();
			}

			// 更新淡入淡出效果
			if (Bodyrelmove::isFading) {
				Bodyrelmove::RenderFadeEffect();
			}
		}

		ImGui::End();

		// 恢复样式
		ImGui::PopStyleColor(2);
		ImGui::PopStyleVar();
	}

	// 在最后绘制光标
	if (KeyBoard::imguiMainEnabled)
	{
		// 动画计时
		cursorAnimTimer += ImGui::GetIO().DeltaTime;
		if (cursorAnimTimer > cursorAnimInterval) {
			cursorAnimFrame = (cursorAnimFrame + 1) % cursorAnimFrameCount;
			cursorAnimTimer = 0.0f;
		}
		// 第三个窗体动画帧切换
		const int thereBgAnimFrameCount = 3; // 帧数
		thereBgAnimTimer += ImGui::GetIO().DeltaTime;
		if (thereBgAnimTimer > thereBgAnimInterval) {
			thereBgAnimFrame = (thereBgAnimFrame + 1) % thereBgAnimFrameCount;
			thereBgAnimTimer = 0.0f;
		}
		POINT p;
		if (GetCursorPos(&p) && ScreenToClient(DirectX9Hook::GetWindowHandle(), &p))
		{
			ImGuiIO& io = ImGui::GetIO();
			if (io.WantCaptureMouse) {
				auto is_mouse_down = ImGui::IsMouseDown(0);
				ImTextureID tex_id = nullptr;
				if (is_mouse_down && DirectX9Hook::texture_cursor_clicked) {
					tex_id = DirectX9Hook::texture_cursor_clicked;
				}
				else {
					// 这里用动画帧
					switch (cursorAnimFrame) {
					//case 0: tex_id = texture_cursor_anim1; break;
					//case 1: tex_id = texture_cursor_anim2; break;
					case 0: tex_id = texture_cursor_anim3; break;
					case 1: tex_id = texture_cursor_anim4; break;
					}
				}
				if (tex_id) {
					ImGui::GetForegroundDrawList()->AddImage(
						tex_id,
						ImVec2((float)p.x, (float)p.y - 3),
						ImVec2((float)p.x + 24, (float)p.y + 24)
					);
				}
			}
		}

	}

	// 把第二个窗口的代码移到这里，完全独立于第一个窗口的显示控制
	if (BossHP::dBossHpPercentage > 0 && DirectX9Hook::texture_bosshpbar)  // 只在有BOSS时显示窗口
	{


		// 设置第二个窗口的位置和大小
		int miniMapWidth = BossHP::GetMiniMapWidth();
		ImVec2 window_pos(miniMapWidth + 35, 18);

		// 使用图片的实际分辨率
		float bgWidth = 385.0f;   // 原始宽度
		float bgHeight = 33.0f;   // 原始高度

		// 计算显示尺寸，保持原始比例
		float displayWidth = 250.0f;  // 期望显示宽度
		float displayHeight = (displayWidth / bgWidth) * bgHeight;  // 按比例计算高度

		ImVec2 window_size(displayWidth, displayHeight);

		// 如果背景纹理存在，先绘制背景 
		ImDrawList* draw_list = ImGui::GetBackgroundDrawList();
		draw_list->AddImage(
			DirectX9Hook::texture_bosshpbar,
			window_pos,
			ImVec2(window_pos.x + window_size.x, window_pos.y + window_size.y),
			ImVec2(0, 0),          // UV坐标起点
			ImVec2(1, 1),          // UV坐标终点 - 使用完整图片
			IM_COL32(255, 255, 255, 255)
		);

		// 使窗口完全透明
		ImGui::PushStyleColor(ImGuiCol_WindowBg, ImVec4(0.0f, 0.0f, 0.0f, 0.0f));
		ImGui::PushStyleColor(ImGuiCol_Border, ImVec4(0.0f, 0.0f, 0.0f, 0.0f));
		ImGui::PushStyleVar(ImGuiStyleVar_Alpha, 1.0f); // <--- 新增，确保不透明

		ImGui::SetNextWindowPos(window_pos);
		ImGui::SetNextWindowSize(window_size);

		if (ImGui::Begin("##SecondWindow", nullptr,
			ImGuiWindowFlags_NoTitleBar |
			ImGuiWindowFlags_NoResize |
			ImGuiWindowFlags_NoMove))
		{
			char sToolTip[50];
			sprintf_s(sToolTip, sizeof(sToolTip), "%lld   ( %.2f%%%% )",
				//BossHP::dBossHpPercentage,
				BossHP::currentHP,
				//BossHP::maxHP);
				BossHP::dBossHpPercentage);

			ImGui::SetCursorPosY(ImGui::GetCursorPosY() - 1.5);
			ImGui::SetCursorPosX(ImGui::GetCursorPosX() + 95);
			ImGui::PushFont(g_normalFont); //ImGui::PushFont(g_boldFont);
			ImGui::TextColored(ImVec4(1.0f, 1.0f, 1.0f, 1.0f), sToolTip);
			ImGui::PopFont();


			ImGui::End();
		}

		ImGui::PopStyleColor(2);
	}
	// 第三个窗口的显示条件（可自定义）
	if (DirectX9Hook::there_background) {
		D3DSURFACE_DESC desc;
		DirectX9Hook::there_background->GetLevelDesc(0, &desc);
		ImVec2 img_size((float)desc.Width, (float)desc.Height);

		ImGui::SetNextWindowPos(ImVec2(1, 320));
		ImGui::SetNextWindowSize(img_size);
		ImGui::PushStyleVar(ImGuiStyleVar_Alpha, 1.0f); // <--- 新增，确保不透明

		ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(0, 0)); // 取消内边距
		ImGui::PushStyleColor(ImGuiCol_WindowBg, ImVec4(0, 0, 0, 0));   // 透明背景
		ImGui::PushStyleColor(ImGuiCol_Border, ImVec4(0, 0, 0, 0));     // 透明边框
		if (ImGui::Begin("##ThirdWindow", nullptr,
			ImGuiWindowFlags_NoTitleBar |
			ImGuiWindowFlags_NoResize |
			ImGuiWindowFlags_NoMove |
			ImGuiWindowFlags_NoScrollbar |
			ImGuiWindowFlags_NoScrollWithMouse))
		{
			ImTextureID tex_id = nullptr;
			switch (thereBgAnimFrame) {
			case 0: tex_id = DirectX9Hook::there_background_anim0; break;
			case 1: tex_id = DirectX9Hook::there_background_anim1; break;
			case 2: tex_id = DirectX9Hook::there_background_anim2; break;
			case 3: tex_id = DirectX9Hook::there_background_anim3; break;
			case 4: tex_id = DirectX9Hook::there_background_anim4; break;
			case 5: tex_id = DirectX9Hook::there_background_anim5; break;
			case 6: tex_id = DirectX9Hook::there_background_anim6; break;
			case 7: tex_id = DirectX9Hook::there_background_anim7; break;
			}
			if (tex_id)
				ImGui::Image(tex_id, img_size);
			// 新增：点击第三个窗体时切换显示
			if (ImGui::IsWindowHovered() && ImGui::IsMouseClicked(0)) {
				bool newState = !(showFirstWindowAndBg && showDamageWindow);
				showFirstWindowAndBg = newState;
				showDamageWindow = newState;
			}
			ImGui::End();
		}
		ImGui::PopStyleVar();
	}



	// 结束 ImGui 帧并渲染
	ImGui::EndFrame();
	ImGui::Render();
	pIDirect3DDevice9->BeginScene();
	ImGui_ImplDX9_RenderDrawData(ImGui::GetDrawData());
	pIDirect3DDevice9->EndScene();

	Memory::SetHook(true, reinterpret_cast<void**>(&OldEndScene), GrkEndScene);

	return hRet;
}

void DirectX9Hook::Initialize() {
	//std::cout << "Starting DirectX9Hook initialization..." << std::endl;

	g_hWnd = FindWindowA("MapleStoryClass", "WhiteMs");
	if (!g_hWnd) {
		return;
	}


	// 在初始化时隐藏系统光标
	ShowCursor(FALSE);

	// 确保窗口已经完全创建
	if (!IsWindow(g_hWnd)) {
		return;
	}

	g_IDirect3D9 = Direct3DCreate9(D3D_SDK_VERSION);
	if (!g_IDirect3D9) {
		return;
	}

	memset(&g_d3dpp, 0, sizeof(g_d3dpp));
	g_d3dpp.Windowed = TRUE;
	g_d3dpp.SwapEffect = D3DSWAPEFFECT_DISCARD;
	g_d3dpp.BackBufferFormat = D3DFMT_UNKNOWN;
	g_d3dpp.EnableAutoDepthStencil = TRUE;
	g_d3dpp.AutoDepthStencilFormat = D3DFMT_D16;

	// 获取窗口客户区大小
	RECT clientRect;
	GetClientRect(g_hWnd, &clientRect);
	screenWidth = clientRect.right - clientRect.left;
	screenHeight = clientRect.bottom - clientRect.top;

	HRESULT hRet = g_IDirect3D9->CreateDevice(D3DADAPTER_DEFAULT, D3DDEVTYPE_HAL, g_hWnd,
		D3DCREATE_SOFTWARE_VERTEXPROCESSING, &g_d3dpp, &g_pIDirect3DDevice9);

	if (FAILED(hRet)) {
		g_IDirect3D9->Release();
		g_IDirect3D9 = nullptr;
		return;
	}

	dwDeviceVirtualTable = (DWORD*)*(DWORD*)g_pIDirect3DDevice9;
	OldReset = (FuncReset)dwDeviceVirtualTable[16];
	OldEndScene = (FuncEndScene)dwDeviceVirtualTable[42];
	Memory::SetHook(true, reinterpret_cast<void**>(&OldReset), GrkReset);
	Memory::SetHook(true, reinterpret_cast<void**>(&OldEndScene), GrkEndScene);

	std::cout << "DirectX9Hook 初始化完成!" << std::endl;
}

void DirectX9Hook::Shutdown() {
	// 恢复原始窗口过程
	if (OldWndProc && g_hWnd) {
		SetWindowLongPtr(g_hWnd, GWL_WNDPROC, (LONG_PTR)OldWndProc);
		OldWndProc = nullptr;
	}

	// 移除钩子
	Memory::SetHook(false, reinterpret_cast<void**>(&OldReset), GrkReset);
	Memory::SetHook(false, reinterpret_cast<void**>(&OldEndScene), GrkEndScene);
	if (DirectX9Hook::texture_cursor) {
		DirectX9Hook::texture_cursor->Release();
		DirectX9Hook::texture_cursor = nullptr;
	}
	if (DirectX9Hook::texture_cursor_clicked) {
		DirectX9Hook::texture_cursor_clicked->Release();
		DirectX9Hook::texture_cursor_clicked = nullptr;
	}
	if (texture_cursor_anim1) {
		texture_cursor_anim1->Release();
		texture_cursor_anim1 = nullptr;
	}
	if (texture_cursor_anim2) {
		texture_cursor_anim2->Release();
		texture_cursor_anim2 = nullptr;
	}
	if (texture_cursor_anim3) {
		texture_cursor_anim3->Release();
		texture_cursor_anim3 = nullptr;
	}
	if (texture_cursor_anim4) {
		texture_cursor_anim4->Release();
		texture_cursor_anim4 = nullptr;
	}
	if (DirectX9Hook::texture_bosshpbar) {
		DirectX9Hook::texture_bosshpbar->Release();
		DirectX9Hook::texture_bosshpbar = nullptr;
	}
	if (DirectX9Hook::texture_background) {
		DirectX9Hook::texture_background->Release();
		DirectX9Hook::texture_background = nullptr;
	}
	if (DirectX9Hook::there_background) {
		DirectX9Hook::there_background->Release();
		DirectX9Hook::there_background = nullptr;
	}
#define SAFE_RELEASE_TEXTURE(tex) if (tex) { tex->Release(); tex = nullptr; }
	SAFE_RELEASE_TEXTURE(DirectX9Hook::there_background_anim0);
	SAFE_RELEASE_TEXTURE(DirectX9Hook::there_background_anim1);
	SAFE_RELEASE_TEXTURE(DirectX9Hook::there_background_anim2);
	SAFE_RELEASE_TEXTURE(DirectX9Hook::there_background_anim3);
	SAFE_RELEASE_TEXTURE(DirectX9Hook::there_background_anim4);
	SAFE_RELEASE_TEXTURE(DirectX9Hook::there_background_anim5);
	SAFE_RELEASE_TEXTURE(DirectX9Hook::there_background_anim6);
	SAFE_RELEASE_TEXTURE(DirectX9Hook::there_background_anim7);


	// 释放排名纹理
	SAFE_RELEASE_TEXTURE(DirectX9Hook::texture_rank1);
	SAFE_RELEASE_TEXTURE(DirectX9Hook::texture_rank2);
	SAFE_RELEASE_TEXTURE(DirectX9Hook::texture_rank3);
	SAFE_RELEASE_TEXTURE(DirectX9Hook::texture_rank4);
	SAFE_RELEASE_TEXTURE(DirectX9Hook::texture_rank5);
	SAFE_RELEASE_TEXTURE(DirectX9Hook::texture_rank6);
	SAFE_RELEASE_TEXTURE(DirectX9Hook::texture_rank7);
	SAFE_RELEASE_TEXTURE(DirectX9Hook::texture_rank8);
	SAFE_RELEASE_TEXTURE(DirectX9Hook::texture_rank9);
	SAFE_RELEASE_TEXTURE(DirectX9Hook::texture_rank10);

	// 释放按钮背景纹理
	SAFE_RELEASE_TEXTURE(DirectX9Hook::button_background);
	SAFE_RELEASE_TEXTURE(DirectX9Hook::button_background_hovered);
	SAFE_RELEASE_TEXTURE(DirectX9Hook::button_background_pressed);

	// 释放BOSSDPS窗口相关纹理
	SAFE_RELEASE_TEXTURE(bossDpsWindowBackground);
	SAFE_RELEASE_TEXTURE(bossDpsCloseBtnTextureNormal);
	SAFE_RELEASE_TEXTURE(bossDpsCloseBtnTextureHovered);
	SAFE_RELEASE_TEXTURE(bossDpsCloseBtnTexturePressed);
	SAFE_RELEASE_TEXTURE(bossDpsCloseBtnTextureDisabled);

	start_button_texture.reset();
	end_button_texture.reset();
	// 清理宏定义
#undef SAFE_RELEASE_TEXTURE
	// 清理ImGui
	ImGui_ImplDX9_Shutdown();
	ImGui_ImplWin32_Shutdown();
	ImGui::DestroyContext();

	// 释放D3D设备和接口
	if (g_pIDirect3DDevice9) {
		g_pIDirect3DDevice9->Release();
		g_pIDirect3DDevice9 = nullptr;
	}
	if (g_IDirect3D9) {
		g_IDirect3D9->Release();
		g_IDirect3D9 = nullptr;
	}

	// 重置其他全局变量
	g_hWnd = NULL;
	dwDeviceVirtualTable = NULL;
	OldReset = NULL;
	OldEndScene = NULL;
}