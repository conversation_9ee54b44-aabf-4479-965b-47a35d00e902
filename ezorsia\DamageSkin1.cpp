#include "DamageSkin1.h"
#include "GameObject.h"
#include <map>
#include "Memory.h"
#include <iostream>
#include "CInPacket.hpp"
#include <windows.h>

// ==================== 伤害皮肤系统 v1.0 ====================
// 功能：自定义伤害数字和MISS效果显示
// 作者：优化版本
// 状态：稳定运行

// 配置选项
static const bool ENABLE_DEBUG_OUTPUT = false;  // 设置为false可关闭调试输出

struct DamageSkinProp {
	IWzPropertyPtr pNoRed0;
	IWzPropertyPtr pNoRed1;
	IWzPropertyPtr pNoCri0;
	IWzPropertyPtr pNoCri1;
	IWzPropertyPtr pMiss;  // 添加MISS资源
};

static std::map<int, DamageSkinProp> g_mDamageSkinProp;
int DamageSkin1::g_nDamageSkin = 0;
bool DamageSkin1::isInitialized = false;

void DamageSkin1::LoadDamageSkin() {
	std::cout << "=== 伤害皮肤系统加载完成 ===" << std::endl;
	std::cout << "已加载 " << g_mDamageSkinProp.size() << " 个伤害皮肤" << std::endl;
	Ztl_variant_t vEmpty;
	Ztl_variant_t vDamageSkin;
	wchar_t pathDamgeSkin[] = L"Effect/DamageSkin.img";
	CHECK_HRESULT(get_rm()->raw_GetObject(pathDamgeSkin, vEmpty, vEmpty, &vDamageSkin));
	IWzPropertyPtr pDamageSkin = IWzPropertyPtr(vDamageSkin.GetUnknown(false, false));
	// iterate damage skins
	IUnknownPtr pEnumUnknown;
	CHECK_HRESULT(pDamageSkin->get__NewEnum(&pEnumUnknown));
	IEnumVARIANTPtr pEnum = IEnumVARIANTPtr(pEnumUnknown);
	while (1) {
		VARIANT rgVar[1];
		ULONG uCeltFetched;
		if (FAILED(pEnum->Next(1, rgVar, &uCeltFetched)) || uCeltFetched == 0) {
			break;
		}
		// load damage skin props
		int nID = wcstol(rgVar[0].bstrVal, nullptr, 10);
		Ztl_variant_t vProp;
		CHECK_HRESULT(pDamageSkin->get_item(rgVar[0].bstrVal, &vProp));
		IWzPropertyPtr pProp = IWzPropertyPtr(vProp.GetUnknown(false, false));
		Ztl_variant_t vNoRed0, vNoRed1, vNoCri0, vNoCri1, vMiss;
		wchar_t szNoRed0[] = L"NoRed0", szNoRed1[] = L"NoRed1", szNoCri0[] = L"NoCri0", szNoCri1[] = L"NoCri1", szMiss[] = L"Miss";
		CHECK_HRESULT(pProp->get_item(szNoRed0, &vNoRed0));
		CHECK_HRESULT(pProp->get_item(szNoRed1, &vNoRed1));
		CHECK_HRESULT(pProp->get_item(szNoCri0, &vNoCri0));
		CHECK_HRESULT(pProp->get_item(szNoCri1, &vNoCri1));

		// 加载MISS资源 (位于NoRed0中)
		IWzPropertyPtr pMissPtr = nullptr;
		IWzPropertyPtr pNoRed0 = IWzPropertyPtr(vNoRed0.GetUnknown(false, false));

		if (pNoRed0 != nullptr) {
			Ztl_variant_t vMissInNoRed0;
			if (SUCCEEDED(pNoRed0->get_item(szMiss, &vMissInNoRed0))) {
				pMissPtr = IWzPropertyPtr(vMissInNoRed0.GetUnknown(false, false));
			}

			// 如果没有独立的Miss资源，使用NoRed0作为MISS资源
			if (pMissPtr == nullptr) {
				pMissPtr = pNoRed0;
			}
		}

		g_mDamageSkinProp[nID] = DamageSkinProp{
			IWzPropertyPtr(vNoRed0.GetUnknown(false, false)),
			IWzPropertyPtr(vNoRed1.GetUnknown(false, false)),
			IWzPropertyPtr(vNoCri0.GetUnknown(false, false)),
			IWzPropertyPtr(vNoCri1.GetUnknown(false, false)),
			pMissPtr
		};
	}
}

static uintptr_t CAnimationDisplayer__Effect_HP_jmp = 0x00437D35; // bCriticalAttack check
static uintptr_t CAnimationDisplayer__Effect_HP_ret = 0x00437DBE;

// MISS显示相关的Hook地址 - 尝试不同的候选地址
// 基于你的IDA搜索，尝试这些地址：
static uintptr_t CAnimationDisplayer__Effect_Miss_jmp = 0x00438A21; // IDA分析的地址
// 备用地址（如果上面不工作，可以尝试这些）：
// 0x00438B26, 0x00438B15, 0x00438AF8, 0x00438AE0, 0x00438AC8, 0x00438A66, 0x00438A51
static uintptr_t CAnimationDisplayer__Effect_Miss_ret = 0x00438A2B;

void __stdcall CAnimationDisplayer__Effect_HP_helper(unsigned pThis, int lColorType, int bCriticalAttack, IWzPropertyPtr& pEffSmall, IWzPropertyPtr& pEffLarge) {
	//std::cout << "g_nDamageSkin: " << DamageSkin1::g_nDamageSkin << std::endl;
	if (auto search = g_mDamageSkinProp.find(DamageSkin1::g_nDamageSkin); search != g_mDamageSkinProp.end()) {
		if (lColorType == 1) {  // blue 
			pEffLarge = *reinterpret_cast<IWzPropertyPtr*>(reinterpret_cast<DWORD*>(pThis) + 0x5F); // blue1
			pEffSmall = *reinterpret_cast<IWzPropertyPtr*>(reinterpret_cast<DWORD*>(pThis) + 0x5E); // blue0
		}
		else if (lColorType != 2) {  // not violet
			if (bCriticalAttack) {
				pEffSmall = search->second.pNoCri0;
				pEffLarge = search->second.pNoCri1;
			}
			else {
				pEffSmall = search->second.pNoRed0;
				pEffLarge = search->second.pNoRed1;
			}
		}
		else {  // violet 
			pEffLarge = *reinterpret_cast<IWzPropertyPtr*>(reinterpret_cast<DWORD*>(pThis) + 0x61); // violet1
			pEffSmall = *reinterpret_cast<IWzPropertyPtr*>(reinterpret_cast<DWORD*>(pThis) + 0x60); // violet0
		}
		//std::cout << "found damage skin" << std::endl;
		//if (bCriticalAttack & 1) {
		//	pEffSmall = search->second.pNoCri0;
		//	pEffLarge = search->second.pNoCri1;
		//}
		//else {
		//	pEffSmall = search->second.pNoRed0;
		//	pEffLarge = search->second.pNoRed1;
		//}
	}
	else {
	//	std::cout << "not found damage skin" << std::endl;
		if (lColorType == 1) {  // blue
			pEffLarge = *reinterpret_cast<IWzPropertyPtr*>(reinterpret_cast<DWORD*>(pThis) + 0x5F); // blue1
			pEffSmall = *reinterpret_cast<IWzPropertyPtr*>(reinterpret_cast<DWORD*>(pThis) + 0x5E); // blue0
		}
		else if (lColorType != 2) {  // not violet
			if (bCriticalAttack) {
				pEffLarge = *reinterpret_cast<IWzPropertyPtr*>(reinterpret_cast<DWORD*>(pThis) + 0x63); // cri1
				pEffSmall = *reinterpret_cast<IWzPropertyPtr*>(reinterpret_cast<DWORD*>(pThis) + 0x62); // cri0
			}
			else {
				pEffLarge = *reinterpret_cast<IWzPropertyPtr*>(reinterpret_cast<DWORD*>(pThis) + 0x5D); // red1
				pEffSmall = *reinterpret_cast<IWzPropertyPtr*>(reinterpret_cast<DWORD*>(pThis) + 0x5C); // red0
			}
		}
		else {  // violet
			pEffLarge = *reinterpret_cast<IWzPropertyPtr*>(reinterpret_cast<DWORD*>(pThis) + 0x60); // violet1
			pEffSmall = *reinterpret_cast<IWzPropertyPtr*>(reinterpret_cast<DWORD*>(pThis) + 0x61); // violet0
		}
	} 
}

void __declspec(naked) CAnimationDisplayer__Effect_HP_hook() {
	__asm {
		// save registers
		pushad
		pushfd
		// call helper function
		lea     eax, [ebp - 0x18];	pEffLarge
		push    eax
		lea     eax, [ebp - 0x20];	pEffSmall
		push    eax
		mov     eax, [ebp + 0x18];	bCriticalAttack
		push    eax
		mov     eax, [ebp + 0x14];	lColorType
		push    eax
		mov     ecx, esi
		push    esi;				CMob*
		call    CAnimationDisplayer__Effect_HP_helper
		// restore registers and return
		popfd
		popad
		jmp[CAnimationDisplayer__Effect_HP_ret]
	};
}


// 原始MISS函数指针
typedef void(__thiscall* OriginalEffect_Miss_t)(void* this_ptr, long x, long y, long type);
static OriginalEffect_Miss_t OriginalEffect_Miss = nullptr;

// 使用图层动画系统显示自定义MISS - 基于伤害显示的方法
void CallCustomMissEffect(void* this_ptr, long x, long y, IWzPropertyPtr canvas) {
	// 基于IDA分析，使用图层动画系统
	// 这个函数地址来自伤害显示函数中的调用
	typedef void(__thiscall* CreateLayerAndAnimate_t)(void* this_ptr, long x, long y, IWzPropertyPtr canvas);

	// 尝试几个可能的地址（从伤害显示函数中找到的）
	CreateLayerAndAnimate_t CreateLayerAndAnimate = (CreateLayerAndAnimate_t)0x00435D6F; // 从伤害函数末尾看到的调用
	CreateLayerAndAnimate(this_ptr, x, y, canvas);
}

// ==================== MISS Hook 实现 ====================
// 优化版本：安全、简洁、高效
void __fastcall HookedEffect_Miss(void* this_ptr, void* edx, long x, long y, long type) {
	// 快速检查：是否有自定义MISS资源
	auto search = g_mDamageSkinProp.find(DamageSkin1::g_nDamageSkin);
	if (search == g_mDamageSkinProp.end() || search->second.pMiss == nullptr) {
		// 没有自定义MISS，直接使用默认
		if (OriginalEffect_Miss) {
			OriginalEffect_Miss(this_ptr, x, y, type);
		}
		return;
	}

	// 根据type参数确定正确的资源指针偏移量
	uintptr_t offset;
	switch (type) {
		case 0:  offset = 0x5C; break;  // [esi+170h] / 4
		case 2:  offset = 0x60; break;  // [esi+180h] / 4
		default:
			// 未知type，使用默认MISS
			if (OriginalEffect_Miss) {
				OriginalEffect_Miss(this_ptr, x, y, type);
			}
			return;
	}

	// 安全地修改资源指针并调用原始函数
	__try {
		DWORD* pObj = reinterpret_cast<DWORD*>(this_ptr);
		IWzPropertyPtr* pMissPtr = reinterpret_cast<IWzPropertyPtr*>(pObj + offset);

		// 临时替换资源指针
		IWzPropertyPtr originalMiss = *pMissPtr;
		*pMissPtr = search->second.pMiss;

		// 调用原始函数（使用自定义资源）
		if (OriginalEffect_Miss) {
			OriginalEffect_Miss(this_ptr, x, y, type);
		}

		// 恢复原始资源指针
		*pMissPtr = originalMiss;
	}
	__except(EXCEPTION_EXECUTE_HANDLER) {
		// 异常处理：使用默认MISS
		if (OriginalEffect_Miss) {
			OriginalEffect_Miss(this_ptr, x, y, type);
		}
	}
}

// 极简的MISS Hook - 只做最基本的操作
void __declspec(naked) CAnimationDisplayer__Effect_Miss_hook() {
	__asm {
		// 执行被替换的原始指令
		mov     [ebp-10h], ebx		// 对应原始的 mov [ebp+var_10], ebx
		mov     eax, [ebp+10h]		// 对应原始的 mov eax, [ebp+arg_8]

		// 直接跳转到返回地址，不做任何额外操作
		jmp[CAnimationDisplayer__Effect_Miss_ret]
	};
}



// ==================== 伤害皮肤系统初始化 ====================
void DamageSkin1::AttachDamageSkinMod() {
	std::cout << "=== 伤害皮肤系统初始化 ===" << std::endl;

	// 启用伤害Hook
	Memory::CodeCave(reinterpret_cast<void*>(&CAnimationDisplayer__Effect_HP_hook), CAnimationDisplayer__Effect_HP_jmp, 0);
	std::cout << "✓ 伤害Hook已启用" << std::endl;

	// 启用MISS Hook
	OriginalEffect_Miss = reinterpret_cast<OriginalEffect_Miss_t>(CAnimationDisplayer__Effect_Miss_jmp);
	Memory::SetHook(true, reinterpret_cast<void**>(&OriginalEffect_Miss), HookedEffect_Miss);
	std::cout << "✓ MISS Hook已启用" << std::endl;

	// 设置默认皮肤
	g_nDamageSkin = 1;
	std::cout << "✓ 伤害皮肤系统初始化完成，当前皮肤ID: " << g_nDamageSkin << std::endl;
}





void DamageSkin1::AttachMissHook() {
	std::cout << "=== MISS Hook 已启用 ===" << std::endl;
	std::cout << "MISS Hook地址: 0x" << std::hex << CAnimationDisplayer__Effect_Miss_jmp << std::endl;
	TestMissHook();
	TryDifferentMissAddress();
}

void DamageSkin1::SendDamageSkinToServer(int skinId) {
	// 创建发送到服务器的数据包
	COutPacket oPacket(SET_DAMAGE_SKIN_REQUEST); // 0x1001
	oPacket.Encode4(skinId);     // 发送皮肤ID

	// 发送数据包到服务器
	auto pSocket = CClientSocket::GetInstance();
	if (pSocket) {
		pSocket->SendPacket(&oPacket);
		std::cout << "发送伤害皮肤到服务器: " << skinId << std::endl;
	}
}

void DamageSkin1::SetDamageSkin(int skinId) {
	// 设置本地伤害皮肤
	g_nDamageSkin = skinId;
	std::cout << "设置本地伤害皮肤: " << skinId << std::endl;

	// 发送到服务器进行同步
	SendDamageSkinToServer(skinId);
}

void DamageSkin1::TestMissHook() {
	std::cout << "=== 伤害皮肤状态检查 ===" << std::endl;
	std::cout << "当前皮肤ID: " << g_nDamageSkin << std::endl;
	std::cout << "已加载皮肤数量: " << g_mDamageSkinProp.size() << std::endl;

	// 检查当前皮肤的MISS资源
	auto search = g_mDamageSkinProp.find(g_nDamageSkin);
	if (search != g_mDamageSkinProp.end() && search->second.pMiss != nullptr) {
		std::cout << "✓ 当前皮肤包含MISS资源" << std::endl;
	} else {
		std::cout << "✗ 当前皮肤不包含MISS资源" << std::endl;
	}
}

void DamageSkin1::TryDifferentMissAddress() {
	// 此函数已不需要，MISS Hook已稳定工作
	std::cout << "MISS Hook已稳定运行，无需尝试其他地址" << std::endl;
}