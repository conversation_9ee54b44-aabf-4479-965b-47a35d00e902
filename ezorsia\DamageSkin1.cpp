#include "DamageSkin1.h"
#include "GameObject.h"
#include <map>
#include "Memory.h"
#include <iostream>
#include "CInPacket.hpp"
#include <windows.h>

struct DamageSkinProp {
	IWzPropertyPtr pNoRed0;
	IWzPropertyPtr pNoRed1;
	IWzPropertyPtr pNoCri0;
	IWzPropertyPtr pNoCri1;
	IWzPropertyPtr pMiss;  // 添加MISS资源
};

static std::map<int, DamageSkinProp> g_mDamageSkinProp;
int DamageSkin1::g_nDamageSkin = 0;
bool DamageSkin1::isInitialized = false;

void DamageSkin1::LoadDamageSkin() {
	std::cout << "伤害皮肤初始化完成!" << std::endl;
	Ztl_variant_t vEmpty;
	Ztl_variant_t vDamageSkin;
	wchar_t pathDamgeSkin[] = L"Effect/DamageSkin.img";
	CHECK_HRESULT(get_rm()->raw_GetObject(pathDamgeSkin, vEmpty, vEmpty, &vDamageSkin));
	IWzPropertyPtr pDamageSkin = IWzPropertyPtr(vDamageSkin.GetUnknown(false, false));
	// iterate damage skins
	IUnknownPtr pEnumUnknown;
	CHECK_HRESULT(pDamageSkin->get__NewEnum(&pEnumUnknown));
	IEnumVARIANTPtr pEnum = IEnumVARIANTPtr(pEnumUnknown);
	while (1) {
		VARIANT rgVar[1];
		ULONG uCeltFetched;
		if (FAILED(pEnum->Next(1, rgVar, &uCeltFetched)) || uCeltFetched == 0) {
			break;
		}
		// load damage skin props
		int nID = wcstol(rgVar[0].bstrVal, nullptr, 10);
		Ztl_variant_t vProp;
		CHECK_HRESULT(pDamageSkin->get_item(rgVar[0].bstrVal, &vProp));
		IWzPropertyPtr pProp = IWzPropertyPtr(vProp.GetUnknown(false, false));
		Ztl_variant_t vNoRed0, vNoRed1, vNoCri0, vNoCri1, vMiss;
		wchar_t szNoRed0[] = L"NoRed0", szNoRed1[] = L"NoRed1", szNoCri0[] = L"NoCri0", szNoCri1[] = L"NoCri1", szMiss[] = L"Miss";
		CHECK_HRESULT(pProp->get_item(szNoRed0, &vNoRed0));
		CHECK_HRESULT(pProp->get_item(szNoRed1, &vNoRed1));
		CHECK_HRESULT(pProp->get_item(szNoCri0, &vNoCri0));
		CHECK_HRESULT(pProp->get_item(szNoCri1, &vNoCri1));

		// 尝试加载Miss资源，Miss在NoRed0里面
		IWzPropertyPtr pMissPtr = nullptr;

		// 先获取NoRed0，然后从NoRed0中获取Miss
		IWzPropertyPtr pNoRed0 = IWzPropertyPtr(vNoRed0.GetUnknown(false, false));
		if (pNoRed0 != nullptr) {
			Ztl_variant_t vMissInNoRed0;
			if (SUCCEEDED(pNoRed0->get_item(szMiss, &vMissInNoRed0))) {
				pMissPtr = IWzPropertyPtr(vMissInNoRed0.GetUnknown(false, false));
				if (pMissPtr != nullptr) {
					std::cout << "伤害皮肤 " << nID << " 包含MISS资源 (在NoRed0中) - 指针有效" << std::endl;
				} else {
					std::cout << "伤害皮肤 " << nID << " MISS资源指针无效" << std::endl;
				}
			}
			else {
				std::cout << "伤害皮肤 " << nID << " 的NoRed0中不包含MISS资源" << std::endl;
			}
		}
		else {
			std::cout << "伤害皮肤 " << nID << " 无法获取NoRed0资源" << std::endl;
		}

		// 如果MISS资源无效，尝试使用NoRed0作为MISS资源
		if (pMissPtr == nullptr && pNoRed0 != nullptr) {
			std::cout << "伤害皮肤 " << nID << " 使用NoRed0作为MISS资源" << std::endl;
			pMissPtr = pNoRed0;
		}

		g_mDamageSkinProp[nID] = DamageSkinProp{
			IWzPropertyPtr(vNoRed0.GetUnknown(false, false)),
			IWzPropertyPtr(vNoRed1.GetUnknown(false, false)),
			IWzPropertyPtr(vNoCri0.GetUnknown(false, false)),
			IWzPropertyPtr(vNoCri1.GetUnknown(false, false)),
			pMissPtr
		};
	}
}

static uintptr_t CAnimationDisplayer__Effect_HP_jmp = 0x00437D35; // bCriticalAttack check
static uintptr_t CAnimationDisplayer__Effect_HP_ret = 0x00437DBE;

// MISS显示相关的Hook地址 - 尝试不同的候选地址
// 基于你的IDA搜索，尝试这些地址：
static uintptr_t CAnimationDisplayer__Effect_Miss_jmp = 0x00438A21; // IDA分析的地址
// 备用地址（如果上面不工作，可以尝试这些）：
// 0x00438B26, 0x00438B15, 0x00438AF8, 0x00438AE0, 0x00438AC8, 0x00438A66, 0x00438A51
static uintptr_t CAnimationDisplayer__Effect_Miss_ret = 0x00438A2B;

void __stdcall CAnimationDisplayer__Effect_HP_helper(unsigned pThis, int lColorType, int bCriticalAttack, IWzPropertyPtr& pEffSmall, IWzPropertyPtr& pEffLarge) {
	//std::cout << "g_nDamageSkin: " << DamageSkin1::g_nDamageSkin << std::endl;
	if (auto search = g_mDamageSkinProp.find(DamageSkin1::g_nDamageSkin); search != g_mDamageSkinProp.end()) {
		if (lColorType == 1) {  // blue 
			pEffLarge = *reinterpret_cast<IWzPropertyPtr*>(reinterpret_cast<DWORD*>(pThis) + 0x5F); // blue1
			pEffSmall = *reinterpret_cast<IWzPropertyPtr*>(reinterpret_cast<DWORD*>(pThis) + 0x5E); // blue0
		}
		else if (lColorType != 2) {  // not violet
			if (bCriticalAttack) {
				pEffSmall = search->second.pNoCri0;
				pEffLarge = search->second.pNoCri1;
			}
			else {
				pEffSmall = search->second.pNoRed0;
				pEffLarge = search->second.pNoRed1;
			}
		}
		else {  // violet 
			pEffLarge = *reinterpret_cast<IWzPropertyPtr*>(reinterpret_cast<DWORD*>(pThis) + 0x61); // violet1
			pEffSmall = *reinterpret_cast<IWzPropertyPtr*>(reinterpret_cast<DWORD*>(pThis) + 0x60); // violet0
		}
		//std::cout << "found damage skin" << std::endl;
		//if (bCriticalAttack & 1) {
		//	pEffSmall = search->second.pNoCri0;
		//	pEffLarge = search->second.pNoCri1;
		//}
		//else {
		//	pEffSmall = search->second.pNoRed0;
		//	pEffLarge = search->second.pNoRed1;
		//}
	}
	else {
	//	std::cout << "not found damage skin" << std::endl;
		if (lColorType == 1) {  // blue
			pEffLarge = *reinterpret_cast<IWzPropertyPtr*>(reinterpret_cast<DWORD*>(pThis) + 0x5F); // blue1
			pEffSmall = *reinterpret_cast<IWzPropertyPtr*>(reinterpret_cast<DWORD*>(pThis) + 0x5E); // blue0
		}
		else if (lColorType != 2) {  // not violet
			if (bCriticalAttack) {
				pEffLarge = *reinterpret_cast<IWzPropertyPtr*>(reinterpret_cast<DWORD*>(pThis) + 0x63); // cri1
				pEffSmall = *reinterpret_cast<IWzPropertyPtr*>(reinterpret_cast<DWORD*>(pThis) + 0x62); // cri0
			}
			else {
				pEffLarge = *reinterpret_cast<IWzPropertyPtr*>(reinterpret_cast<DWORD*>(pThis) + 0x5D); // red1
				pEffSmall = *reinterpret_cast<IWzPropertyPtr*>(reinterpret_cast<DWORD*>(pThis) + 0x5C); // red0
			}
		}
		else {  // violet
			pEffLarge = *reinterpret_cast<IWzPropertyPtr*>(reinterpret_cast<DWORD*>(pThis) + 0x60); // violet1
			pEffSmall = *reinterpret_cast<IWzPropertyPtr*>(reinterpret_cast<DWORD*>(pThis) + 0x61); // violet0
		}
	} 
}

void __declspec(naked) CAnimationDisplayer__Effect_HP_hook() {
	__asm {
		// save registers
		pushad
		pushfd
		// call helper function
		lea     eax, [ebp - 0x18];	pEffLarge
		push    eax
		lea     eax, [ebp - 0x20];	pEffSmall
		push    eax
		mov     eax, [ebp + 0x18];	bCriticalAttack
		push    eax
		mov     eax, [ebp + 0x14];	lColorType
		push    eax
		mov     ecx, esi
		push    esi;				CMob*
		call    CAnimationDisplayer__Effect_HP_helper
		// restore registers and return
		popfd
		popad
		jmp[CAnimationDisplayer__Effect_HP_ret]
	};
}


// 原始MISS函数指针
typedef void(__thiscall* OriginalEffect_Miss_t)(void* this_ptr, long x, long y, long type);
static OriginalEffect_Miss_t OriginalEffect_Miss = nullptr;

// 使用图层动画系统显示自定义MISS - 基于伤害显示的方法
void CallCustomMissEffect(void* this_ptr, long x, long y, IWzPropertyPtr canvas) {
	// 基于IDA分析，使用图层动画系统
	// 这个函数地址来自伤害显示函数中的调用
	typedef void(__thiscall* CreateLayerAndAnimate_t)(void* this_ptr, long x, long y, IWzPropertyPtr canvas);

	// 尝试几个可能的地址（从伤害显示函数中找到的）
	CreateLayerAndAnimate_t CreateLayerAndAnimate = (CreateLayerAndAnimate_t)0x00435D6F; // 从伤害函数末尾看到的调用
	CreateLayerAndAnimate(this_ptr, x, y, canvas);
}

// MISS Hook函数 - 使用SetHook方法
void __fastcall HookedEffect_Miss(void* this_ptr, void* edx, long x, long y, long type) {
	std::cout << "MISS Hook被调用! 皮肤ID: " << DamageSkin1::g_nDamageSkin << ", 坐标: (" << x << ", " << y << "), 类型: " << type << std::endl;

	// 检查是否有自定义MISS资源
	if (auto search = g_mDamageSkinProp.find(DamageSkin1::g_nDamageSkin); search != g_mDamageSkinProp.end()) {
		if (search->second.pMiss != nullptr) {
			std::cout << "✓ 找到自定义MISS资源，但暂时使用默认MISS避免崩溃" << std::endl;
			// TODO: 实现自定义MISS显示
		}
	}

	// 调用原始MISS函数
	if (OriginalEffect_Miss) {
		OriginalEffect_Miss(this_ptr, x, y, type);
	}
}

// 极简的MISS Hook - 只做最基本的操作
void __declspec(naked) CAnimationDisplayer__Effect_Miss_hook() {
	__asm {
		// 执行被替换的原始指令
		mov     [ebp-10h], ebx		// 对应原始的 mov [ebp+var_10], ebx
		mov     eax, [ebp+10h]		// 对应原始的 mov eax, [ebp+arg_8]

		// 直接跳转到返回地址，不做任何额外操作
		jmp[CAnimationDisplayer__Effect_Miss_ret]
	};
}



void DamageSkin1::AttachDamageSkinMod() {
	Memory::CodeCave(reinterpret_cast<void*>(&CAnimationDisplayer__Effect_HP_hook), CAnimationDisplayer__Effect_HP_jmp, 0);

	// 尝试使用SetHook方法实现MISS Hook
	std::cout << "尝试使用SetHook方法实现MISS Hook..." << std::endl;

	// 设置原函数指针
	OriginalEffect_Miss = reinterpret_cast<OriginalEffect_Miss_t>(CAnimationDisplayer__Effect_Miss_jmp);

	// 使用SetHook
	Memory::SetHook(true, reinterpret_cast<void**>(&OriginalEffect_Miss), HookedEffect_Miss);
	std::cout << "MISS Hook已启用 (SetHook方法)，地址: 0x" << std::hex << CAnimationDisplayer__Effect_Miss_jmp << std::endl;

	// 确保设置一个有效的皮肤ID进行测试
	g_nDamageSkin = 1;
	std::cout << "设置测试皮肤ID为: " << g_nDamageSkin << std::endl;
}





void DamageSkin1::AttachMissHook() {
	std::cout << "=== MISS Hook 已启用 ===" << std::endl;
	std::cout << "MISS Hook地址: 0x" << std::hex << CAnimationDisplayer__Effect_Miss_jmp << std::endl;
	TestMissHook();
	TryDifferentMissAddress();
}

void DamageSkin1::SendDamageSkinToServer(int skinId) {
	// 创建发送到服务器的数据包
	COutPacket oPacket(SET_DAMAGE_SKIN_REQUEST); // 0x1001
	oPacket.Encode4(skinId);     // 发送皮肤ID

	// 发送数据包到服务器
	auto pSocket = CClientSocket::GetInstance();
	if (pSocket) {
		pSocket->SendPacket(&oPacket);
		std::cout << "发送伤害皮肤到服务器: " << skinId << std::endl;
	}
}

void DamageSkin1::SetDamageSkin(int skinId) {
	// 设置本地伤害皮肤
	g_nDamageSkin = skinId;
	std::cout << "设置本地伤害皮肤: " << skinId << std::endl;

	// 发送到服务器进行同步
	SendDamageSkinToServer(skinId);
}

void DamageSkin1::TestMissHook() {
	std::cout << "当前伤害皮肤ID: " << g_nDamageSkin << std::endl;

	// 检查是否有MISS资源
	if (auto search = g_mDamageSkinProp.find(g_nDamageSkin); search != g_mDamageSkinProp.end()) {
		if (search->second.pMiss != nullptr) {
			std::cout << "✓ 当前皮肤包含MISS资源" << std::endl;
		} else {
			std::cout << "✗ 当前皮肤不包含MISS资源" << std::endl;
		}
	} else {
		std::cout << "✗ 未找到当前皮肤" << std::endl;
	}

	// 手动设置为皮肤1进行测试
	std::cout << "手动设置伤害皮肤为ID 1进行测试..." << std::endl;
	g_nDamageSkin = 1;
	std::cout << "设置完成，当前皮肤ID: " << g_nDamageSkin << std::endl;
}

void DamageSkin1::TryDifferentMissAddress() {
	std::cout << "\n=== 尝试不同的MISS Hook地址 ===" << std::endl;

	// 基于你的IDA搜索结果的候选地址
	uintptr_t candidates[] = {
		0x00438A21,  // 当前使用的地址
		0x00438B26,  // 其他可能的地址
		0x00438B15,
		0x00438AF8,
		0x00438AE0,
		0x00438AC8,
		0x00438A66,
		0x00438A51
	};

	std::cout << "候选MISS Hook地址：" << std::endl;
	for (int i = 0; i < sizeof(candidates)/sizeof(candidates[0]); i++) {
		std::cout << "  " << (i+1) << ". 0x" << std::hex << candidates[i];
		if (candidates[i] == CAnimationDisplayer__Effect_Miss_jmp) {
			std::cout << " <- 当前使用";
		}
		std::cout << std::endl;
	}

	std::cout << "\n如果当前Hook不工作，请手动修改代码中的地址并重新编译测试。" << std::endl;
}